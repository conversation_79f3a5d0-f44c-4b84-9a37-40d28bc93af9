﻿using NewLife.Configuration;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace XianAn.Line.Turnplate.UnityHelper
{
    [Config("RunConSetting")]
    public class RunConSetting : Config<RunConSetting>
    {
        /// <summary>
        /// 上拍照数据文件地址
        /// </summary>
        [Description("上拍照数据文件地址")]
        public string UpPictureFileAdd { get; set; } = "F:\\2024\\先安电机\\VS数据\\VS\\Camera\\Results";
        /// <summary>
        /// 下拍照数据文件地址
        /// </summary>
        [Description("下拍照数据文件地址")]
        public string DownPictureFileAdd { get; set; } = "F:\\2024\\先安电机\\VS数据\\VS\\Camera\\Resultsq";
        /// <summary>
        /// 保存数据文件地址
        /// </summary>
        [Description("下拍照数据文件地址")]
        public string SaveFileAdd { get; set; } = "F:\\2024\\先安电机\\VS数据\\VS\\Camera\\Result";
        /// <summary>
        /// 保存数据文件
        /// </summary>
        [Description("下拍照数据文件地址")]
        public string SaveFileName { get; set; } = "Result.csv";

        /// <summary>
        /// 下发编码状态
        /// 1:自动
        /// 2：手动
        /// </summary>
        [Description("下发编码状态")]
        public string IssueStatus { get; set; } = "";

        /// <summary>
        /// 编码
        /// </summary>
        [Description("编码")]
        public string EncodingData { get; set; } = "";

        /// <summary>
        /// 蚀刻机IP
        /// </summary>
        [Description("蚀刻机IP")]
        public string EtchingMachineIP { get; set; } = "*************";
        /// <summary>
        /// 蚀刻机Port
        /// </summary>
        [Description("蚀刻机Port")]
        public int EtchingMachinePort { get; set; } = 9999;
        /// <summary>
        /// 二维码相机IP
        /// </summary>
        [Description("二维码相机IP")]
        public string CameraEWMIP { get; set; } = "*************";
        /// <summary>
        /// 二维码相机Port
        /// </summary>
        [Description("二维码相机Port")]
        public int CameraEWMPort { get; set; } = 10004;
        /// <summary>
        /// 刻码判定等级
        /// </summary>
        [Description("刻码判定等级")]
        public string KMJudgeLimit { get; set; } = "B";
        /// <summary>
        /// 外观检测相机IP
        /// </summary>
        [Description("外观检测相机IP")]
        public string CameraWGJCIP { get; set; } = "************";
        /// <summary>
        /// 外观检测相机Port
        /// </summary>
        [Description("外观检测相机Port")]
        public int CameraWGJCPort { get; set; } = 8500;
        /// <summary>
        /// 标识符检测相机IP
        /// </summary>
        [Description("标识符检测相机IP")]
        public string CameraBSFJCIP { get; set; } = "*************";
        /// <summary>
        /// 标识符检测相机Port
        /// </summary>
        [Description("标识符检测相机Port")]
        public int CameraBSFJCPort { get; set; } = 8500;
        /// <summary>
        /// 高度检测IP
        /// </summary>
        [Description("高度检测IP")]
        public string HeightCheckIP { get; set; } = "*************";
        /// <summary>
        /// 高度检测Port
        /// </summary>
        [Description("高度检测Port")]
        public int HeightCheckPort { get; set; } = 10003;
        /// <summary>
        /// 视觉前高度检测IP
        /// </summary>
        [Description("视觉前高度检测IP")]
        public string HeightCheckIPSJQ { get; set; } = "*************";
        /// <summary>
        /// 视觉前高度检测Port
        /// </summary>
        [Description("视觉前高度检测Port")]
        public int HeightCheckPortSJQ { get; set; } = 10002;
        /// <summary>
        /// PlcIP
        /// </summary>
        [Description("PlcIP")]
        public string PlcIP { get; set; } = "************";
        /// <summary>
        /// PlcPort
        /// </summary>
        [Description("PlcPort")]
        public int PlcPort { get; set; } = 502;

        /// <summary>
        /// 耐压仪端口
        /// </summary>
        [Description("耐压仪端口")]
        public string NYYPort { get; set; } = "COM20";
        /// <summary>
        /// 耐压仪试验电压
        /// </summary>
        [Description("耐压仪试验电压")]
        public ushort NyyVoltage { get; set; } = 5000;
        /// <summary>
        /// 耐压仪电阻量程
        /// </summary>
        [Description("耐压仪电阻量程")]
        public ushort IrRange { get; set; } = 4;
        /// <summary>
        /// 耐压仪电阻上限
        /// </summary>
        [Description("耐压仪电阻上限")]
        public int IrUpperLimit { get; set; } = 1;
        /// <summary>
        /// 耐压仪电阻下限
        /// </summary>
        [Description("耐压仪电阻下限")]
        public int IrLowerLimit { get; set; } = 20000;
        /// <summary>
        /// 耐压仪测试时间
        /// </summary>
        [Description("耐压仪测试时间")]
        public double IrTestTime { get; set; } = 5;
        /// <summary>
        /// 耐压仪试验项
        /// </summary>
        [Description("耐压仪测试时间")]
        public int TestItem { get; set; } = 1;

        /// <summary>
        /// 外观检查默认合格
        /// </summary>
        [Description("外观检查默认合格")]
        public string PassWGJC { get; set; } = "false";
        /// <summary>
        /// 二维码跳过重复编码验证
        /// </summary>
        [Description("二维码跳过重复编码验证")]
        public string PassCFBM { get; set; } = "false";


        /// <summary>
        /// 本地时间
        /// </summary>
        [Description("本地时间")]
        public string TestTime { get; set; } = "";
        /// <summary>
        /// 刻印序号
        /// </summary>
        [Description("刻印序号")]
        public int IndexKY { get; set; } = 0;

        /// <summary>
        /// 历史时间(用于更新编号)
        /// </summary>
        [Description("历史时间")]
        public string HistoryDateTime { get; set; } = "20000101";

        /// <summary>
        /// 高度检测
        /// </summary>
        [Description("高度检测")]
        public string GDJC { get; set; } = "true";
        /// <summary>
        /// 外观检测
        /// </summary>
        [Description("外观检测")]
        public string WGJC { get; set; } = "true";
        /// <summary>
        /// 耐压
        /// </summary>
        [Description("耐压")]
        public string NYJC { get; set; } = "true";
        /// <summary>
        /// 蚀刻机刻印
        /// </summary>
        [Description("蚀刻机刻印")]
        public string SKJKY { get; set; } = "true";
        /// <summary>
        /// 二维码检测
        /// </summary>
        [Description("二维码检测")]
        public string EWMJC { get; set; } = "true";
        /// <summary>
        /// 同轴度检测
        /// </summary>
        [Description("同轴度检测")]
        public string TZDJC { get; set; } = "true";

        /// <summary>
        /// 重复编号检测
        /// </summary>
        [Description("重复编号检测")]
        public string CFBHJC { get; set; } = "true";
    }
}
