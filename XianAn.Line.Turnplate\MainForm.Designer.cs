﻿namespace XianAn.Line.Turnplate
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            uiSmoothLabel2 = new Sunny.UI.UISmoothLabel();
            Header.SuspendLayout();
            SuspendLayout();
            // 
            // Footer
            // 
            Footer.FillColor = SystemColors.ButtonFace;
            Footer.FillColor2 = Color.FromArgb(238, 251, 250);
            Footer.Location = new Point(250, 1024);
            Footer.RectColor = Color.FromArgb(0, 190, 172);
            Footer.Size = new Size(1670, 56);
            Footer.Style = Sunny.UI.UIStyle.Custom;
            Footer.TextAlignment = ContentAlignment.MiddleRight;
            // 
            // Aside
            // 
            Aside.SelectedForeColor = Color.FromArgb(0, 190, 172);
            Aside.SelectedHighColor = Color.FromArgb(0, 190, 172);
            Aside.Size = new Size(250, 935);
            Aside.Style = Sunny.UI.UIStyle.Custom;
            Aside.MenuItemClick += Aside_MenuItemClick;
            // 
            // Header
            // 
            Header.Controls.Add(uiSmoothLabel2);
            Header.Size = new Size(1920, 110);
            // 
            // uiSmoothLabel2
            // 
            uiSmoothLabel2.BackColor = Color.FromArgb(240, 240, 240);
            uiSmoothLabel2.Font = new Font("微软雅黑", 25.8F, FontStyle.Bold, GraphicsUnit.Point);
            uiSmoothLabel2.ForeColor = Color.FromArgb(48, 48, 48);
            uiSmoothLabel2.Location = new Point(3, 23);
            uiSmoothLabel2.Name = "uiSmoothLabel2";
            uiSmoothLabel2.RectColor = Color.FromArgb(0, 190, 172);
            uiSmoothLabel2.Size = new Size(610, 66);
            uiSmoothLabel2.Style = Sunny.UI.UIStyle.Custom;
            uiSmoothLabel2.TabIndex = 63;
            uiSmoothLabel2.Text = "自动生产线调度控制系统";
            // 
            // MainForm
            // 
            AutoScaleMode = AutoScaleMode.None;
            BackColor = Color.FromArgb(238, 251, 250);
            ClientSize = new Size(1920, 1080);
            ControlBoxFillHoverColor = Color.FromArgb(51, 203, 189);
            Name = "MainForm";
            RectColor = Color.FromArgb(0, 190, 172);
            Style = Sunny.UI.UIStyle.Custom;
            Text = "转盘调度系统";
            TitleColor = Color.FromArgb(0, 190, 172);
            ZoomScaleRect = new Rectangle(19, 19, 800, 450);
            Header.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private Sunny.UI.UISmoothLabel uiSmoothLabel2;
    }
}