﻿using SuperSocket.ProtoBase;
using SuperSocket.Server.Abstractions;
using SuperSocket.Server;
using SuperSocket.Server.Host;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NLog;
using Microsoft.Extensions.Hosting;
using System.Xml;
using XianA.Driver.EtchingMachineJPT.Parameters;
using SuperSocket.Connection;

namespace XianA.Driver.EtchingMachineJPT
{
    public class JPTDriver
    {
        /// <summary>
        /// 日志
        /// </summary>
        private Logger logger = LogManager.GetCurrentClassLogger();

        public SuperSocket.Server.Abstractions.Host.ISuperSocketHostBuilder server;

        public delegate MarkingParam GetMarkingParamTrigger();
        public event GetMarkingParamTrigger GetMarkingParamEvent;

        public delegate void SendDataFinishTrigger(string type,bool res);
        public event SendDataFinishTrigger SendDataFinishEvent;

        public void InitService(string ip,int port)
        {
            try
            {
                var server = SuperSocketHostBuilder.Create<StringPackageInfo, DelimiterPipelineFilter>()
                    .UsePackageHandler(async (session, package) =>
                    {
                        try
                        {
                            //Console.WriteLine($"Received: {package.Text}");
                            var data = package.Body;
                            var res = DataProcessing(data, out string value);
                            if (res)
                            {
                                var returnData = value;
                                await session.SendAsync(Encoding.Unicode.GetBytes(returnData));
                                //await session.SendAsync(returnData);
                            }
                            SendDataFinishEvent.Invoke(data, res);
                            //await session.CloseAsync(CloseReason.ServerShutdown);
                        }
                        catch (Exception ex) 
                        {
                            logger.Error(ex);
                        }
                        finally
                        {
                            // 访问结束后断开客户端连接
                            //await session.CloseAsync(CloseReason.LocalClosing);
                        }

                    }).ConfigureSuperSocket(options =>
                    {
                        options.AddListener(new ListenOptions
                        {
                            Ip = ip, // 监听所有 IP 地址
                            Port = port    // 监听端口
                        });
                    }).Build();

                server.RunAsync();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
            }
        }

        public bool DataProcessing(string backData, out string value)
        {
            value = null;
            try
            {
                var data = GetMarkingParamEvent.Invoke();
                if (data == null)
                {
                    return false;
                }
                else if(backData == "bsf")
                {
                    value = data.Identifier;
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }
    }
}
