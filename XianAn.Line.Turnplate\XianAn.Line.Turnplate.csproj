﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.20" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="7.0.20" />
    <PackageReference Include="NewLife.Core" Version="11.0.2024.1115" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.15" />
    <PackageReference Include="SunnyUI" Version="3.8.0.1" />
    <PackageReference Include="Wisdom.Utils" Version="2.3.0" />
    <PackageReference Include="Wisdom.Utils.Driver" Version="3.4.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\XianA.Driver.EtchingMachineJPT\XianA.Driver.EtchingMachineJPT.csproj" />
    <ProjectReference Include="..\XianA.Driver.EtchingMachine\XianA.Driver.EtchingMachine.csproj" />
    <ProjectReference Include="..\XianAn.Driver.CameraEWM\XianAn.Driver.CameraEWM.csproj" />
    <ProjectReference Include="..\XianAn.Driver.CameraJNS\XianAn.Driver.CameraJNS.csproj" />
    <ProjectReference Include="..\XianAn.Driver.HeightCheck\XianAn.Driver.HeightCheck.csproj" />
    <ProjectReference Include="..\XianAn.Driver.NY\XianAn.Driver.NY.csproj" />
    <ProjectReference Include="..\XianAn.Driver.PLC\XianAn.Driver.PLC.csproj" />
    <ProjectReference Include="..\XianAn.Line.Turnplate.Dal\XianAn.Line.Turnplate.Dal.csproj" />
    <ProjectReference Include="..\XianAn.Line.Turnplate.UnityHelper\XianAn.Line.Turnplate.UnityHelper.csproj" />
  </ItemGroup>

</Project>