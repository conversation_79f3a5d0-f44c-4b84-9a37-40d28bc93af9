﻿namespace XianAn.Line.Turnplate
{
    partial class SystemSetControl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            uiTabControl1 = new Sunny.UI.UITabControl();
            tabPage1 = new TabPage();
            uiTextBox1 = new Sunny.UI.UITextBox();
            uiButton5 = new Sunny.UI.UIButton();
            uiLabel1 = new Sunny.UI.UILabel();
            uiButton6 = new Sunny.UI.UIButton();
            uiButton2 = new Sunny.UI.UIButton();
            uiButton7 = new Sunny.UI.UIButton();
            uiLabel2 = new Sunny.UI.UILabel();
            uiButton4 = new Sunny.UI.UIButton();
            uiTextBox2 = new Sunny.UI.UITextBox();
            uiTextBox3 = new Sunny.UI.UITextBox();
            uiButton3 = new Sunny.UI.UIButton();
            uiLabel3 = new Sunny.UI.UILabel();
            tabPage3 = new TabPage();
            tabPage2 = new TabPage();
            uiTabControl1.SuspendLayout();
            tabPage1.SuspendLayout();
            SuspendLayout();
            // 
            // uiTabControl1
            // 
            uiTabControl1.Controls.Add(tabPage1);
            uiTabControl1.Controls.Add(tabPage3);
            uiTabControl1.Controls.Add(tabPage2);
            uiTabControl1.DrawMode = TabDrawMode.OwnerDrawFixed;
            uiTabControl1.FillColor = Color.FromArgb(238, 251, 250);
            uiTabControl1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTabControl1.ItemSize = new Size(150, 40);
            uiTabControl1.Location = new Point(0, 0);
            uiTabControl1.MainPage = "";
            uiTabControl1.Name = "uiTabControl1";
            uiTabControl1.SelectedIndex = 0;
            uiTabControl1.Size = new Size(1670, 839);
            uiTabControl1.SizeMode = TabSizeMode.Fixed;
            uiTabControl1.Style = Sunny.UI.UIStyle.Custom;
            uiTabControl1.TabIndex = 2;
            uiTabControl1.TabSelectedForeColor = Color.FromArgb(0, 190, 172);
            uiTabControl1.TabSelectedHighColor = Color.FromArgb(0, 190, 172);
            uiTabControl1.TabUnSelectedForeColor = Color.FromArgb(240, 240, 240);
            uiTabControl1.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point);
            uiTabControl1.Selected += uiTabControl1_Selected;
            uiTabControl1.Enter += uiTabControl1_Enter;
            // 
            // tabPage1
            // 
            tabPage1.BackColor = Color.FromArgb(238, 251, 250);
            tabPage1.Controls.Add(uiTextBox1);
            tabPage1.Controls.Add(uiButton5);
            tabPage1.Controls.Add(uiLabel1);
            tabPage1.Controls.Add(uiButton6);
            tabPage1.Controls.Add(uiButton2);
            tabPage1.Controls.Add(uiButton7);
            tabPage1.Controls.Add(uiLabel2);
            tabPage1.Controls.Add(uiButton4);
            tabPage1.Controls.Add(uiTextBox2);
            tabPage1.Controls.Add(uiTextBox3);
            tabPage1.Controls.Add(uiButton3);
            tabPage1.Controls.Add(uiLabel3);
            tabPage1.Location = new Point(0, 40);
            tabPage1.Name = "tabPage1";
            tabPage1.Size = new Size(1670, 799);
            tabPage1.TabIndex = 0;
            tabPage1.Text = "目录";
            // 
            // uiTextBox1
            // 
            uiTextBox1.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox1.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox1.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox1.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox1.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox1.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox1.ButtonStyleInherited = false;
            uiTextBox1.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox1.Location = new Point(190, 28);
            uiTextBox1.Margin = new Padding(4, 5, 4, 5);
            uiTextBox1.MinimumSize = new Size(1, 16);
            uiTextBox1.Name = "uiTextBox1";
            uiTextBox1.Padding = new Padding(5);
            uiTextBox1.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox1.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox1.ScrollBarStyleInherited = false;
            uiTextBox1.ShowText = false;
            uiTextBox1.Size = new Size(319, 36);
            uiTextBox1.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox1.TabIndex = 4;
            uiTextBox1.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox1.Watermark = "";
            // 
            // uiButton5
            // 
            uiButton5.FillColor = Color.FromArgb(0, 190, 172);
            uiButton5.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton5.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton5.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton5.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton5.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton5.LightColor = Color.FromArgb(238, 251, 250);
            uiButton5.Location = new Point(639, 141);
            uiButton5.MinimumSize = new Size(1, 1);
            uiButton5.Name = "uiButton5";
            uiButton5.RectColor = Color.FromArgb(0, 190, 172);
            uiButton5.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton5.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton5.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton5.Size = new Size(85, 44);
            uiButton5.Style = Sunny.UI.UIStyle.Custom;
            uiButton5.TabIndex = 14;
            uiButton5.Text = "保存";
            uiButton5.Click += uiButton5_Click;
            // 
            // uiLabel1
            // 
            uiLabel1.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel1.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel1.Location = new Point(35, 33);
            uiLabel1.Name = "uiLabel1";
            uiLabel1.Size = new Size(148, 29);
            uiLabel1.Style = Sunny.UI.UIStyle.Custom;
            uiLabel1.TabIndex = 3;
            uiLabel1.Text = "上拍照文件:";
            // 
            // uiButton6
            // 
            uiButton6.FillColor = Color.FromArgb(0, 190, 172);
            uiButton6.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton6.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton6.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton6.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton6.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton6.LightColor = Color.FromArgb(238, 251, 250);
            uiButton6.Location = new Point(639, 83);
            uiButton6.MinimumSize = new Size(1, 1);
            uiButton6.Name = "uiButton6";
            uiButton6.RectColor = Color.FromArgb(0, 190, 172);
            uiButton6.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton6.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton6.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton6.Size = new Size(85, 44);
            uiButton6.Style = Sunny.UI.UIStyle.Custom;
            uiButton6.TabIndex = 13;
            uiButton6.Text = "保存";
            uiButton6.Click += uiButton6_Click;
            // 
            // uiButton2
            // 
            uiButton2.FillColor = Color.FromArgb(0, 190, 172);
            uiButton2.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton2.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton2.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton2.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton2.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton2.LightColor = Color.FromArgb(238, 251, 250);
            uiButton2.Location = new Point(530, 25);
            uiButton2.MinimumSize = new Size(1, 1);
            uiButton2.Name = "uiButton2";
            uiButton2.RectColor = Color.FromArgb(0, 190, 172);
            uiButton2.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton2.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton2.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton2.Size = new Size(85, 44);
            uiButton2.Style = Sunny.UI.UIStyle.Custom;
            uiButton2.TabIndex = 5;
            uiButton2.Text = "选择";
            uiButton2.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton2.Click += uiButton2_Click;
            // 
            // uiButton7
            // 
            uiButton7.FillColor = Color.FromArgb(0, 190, 172);
            uiButton7.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton7.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton7.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton7.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton7.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton7.LightColor = Color.FromArgb(238, 251, 250);
            uiButton7.Location = new Point(639, 25);
            uiButton7.MinimumSize = new Size(1, 1);
            uiButton7.Name = "uiButton7";
            uiButton7.RectColor = Color.FromArgb(0, 190, 172);
            uiButton7.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton7.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton7.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton7.Size = new Size(85, 44);
            uiButton7.Style = Sunny.UI.UIStyle.Custom;
            uiButton7.TabIndex = 12;
            uiButton7.Text = "保存";
            uiButton7.Click += uiButton7_Click;
            // 
            // uiLabel2
            // 
            uiLabel2.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel2.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel2.Location = new Point(35, 90);
            uiLabel2.Name = "uiLabel2";
            uiLabel2.Size = new Size(148, 29);
            uiLabel2.Style = Sunny.UI.UIStyle.Custom;
            uiLabel2.TabIndex = 6;
            uiLabel2.Text = "下拍照文件:";
            // 
            // uiButton4
            // 
            uiButton4.FillColor = Color.FromArgb(0, 190, 172);
            uiButton4.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton4.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton4.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton4.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton4.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton4.LightColor = Color.FromArgb(238, 251, 250);
            uiButton4.Location = new Point(530, 141);
            uiButton4.MinimumSize = new Size(1, 1);
            uiButton4.Name = "uiButton4";
            uiButton4.RectColor = Color.FromArgb(0, 190, 172);
            uiButton4.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton4.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton4.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton4.Size = new Size(85, 44);
            uiButton4.Style = Sunny.UI.UIStyle.Custom;
            uiButton4.TabIndex = 11;
            uiButton4.Text = "选择";
            uiButton4.Click += uiButton4_Click;
            // 
            // uiTextBox2
            // 
            uiTextBox2.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox2.Location = new Point(190, 86);
            uiTextBox2.Margin = new Padding(4, 5, 4, 5);
            uiTextBox2.MinimumSize = new Size(1, 16);
            uiTextBox2.Name = "uiTextBox2";
            uiTextBox2.Padding = new Padding(5);
            uiTextBox2.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox2.ShowText = false;
            uiTextBox2.Size = new Size(319, 36);
            uiTextBox2.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox2.TabIndex = 7;
            uiTextBox2.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox2.Watermark = "";
            // 
            // uiTextBox3
            // 
            uiTextBox3.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox3.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox3.Location = new Point(190, 144);
            uiTextBox3.Margin = new Padding(4, 5, 4, 5);
            uiTextBox3.MinimumSize = new Size(1, 16);
            uiTextBox3.Name = "uiTextBox3";
            uiTextBox3.Padding = new Padding(5);
            uiTextBox3.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox3.ShowText = false;
            uiTextBox3.Size = new Size(319, 36);
            uiTextBox3.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox3.TabIndex = 10;
            uiTextBox3.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox3.Watermark = "";
            // 
            // uiButton3
            // 
            uiButton3.FillColor = Color.FromArgb(0, 190, 172);
            uiButton3.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton3.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton3.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton3.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton3.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton3.LightColor = Color.FromArgb(238, 251, 250);
            uiButton3.Location = new Point(530, 83);
            uiButton3.MinimumSize = new Size(1, 1);
            uiButton3.Name = "uiButton3";
            uiButton3.RectColor = Color.FromArgb(0, 190, 172);
            uiButton3.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton3.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton3.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton3.Size = new Size(85, 44);
            uiButton3.Style = Sunny.UI.UIStyle.Custom;
            uiButton3.TabIndex = 8;
            uiButton3.Text = "选择";
            uiButton3.Click += uiButton3_Click;
            // 
            // uiLabel3
            // 
            uiLabel3.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel3.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel3.Location = new Point(58, 148);
            uiLabel3.Name = "uiLabel3";
            uiLabel3.Size = new Size(125, 29);
            uiLabel3.Style = Sunny.UI.UIStyle.Custom;
            uiLabel3.TabIndex = 9;
            uiLabel3.Text = "结论文件:";
            // 
            // tabPage3
            // 
            tabPage3.BackColor = Color.FromArgb(238, 251, 250);
            tabPage3.Location = new Point(0, 40);
            tabPage3.Name = "tabPage3";
            tabPage3.Size = new Size(1670, 799);
            tabPage3.TabIndex = 2;
            tabPage3.Text = "地址";
            // 
            // tabPage2
            // 
            tabPage2.BackColor = Color.FromArgb(238, 251, 250);
            tabPage2.Location = new Point(0, 40);
            tabPage2.Name = "tabPage2";
            tabPage2.Size = new Size(1670, 799);
            tabPage2.TabIndex = 1;
            tabPage2.Text = "系统控制";
            // 
            // SystemSetControl
            // 
            AutoScaleMode = AutoScaleMode.None;
            BackColor = Color.FromArgb(238, 251, 250);
            ClientSize = new Size(1670, 839);
            ControlBoxFillHoverColor = Color.FromArgb(51, 203, 189);
            Controls.Add(uiTabControl1);
            Name = "SystemSetControl";
            RectColor = Color.FromArgb(0, 190, 172);
            Style = Sunny.UI.UIStyle.Custom;
            uiTabControl1.ResumeLayout(false);
            tabPage1.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private Sunny.UI.UITabControl uiTabControl1;
        private TabPage tabPage1;
        private TabPage tabPage2;
        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UITextBox uiTextBox1;
        private TabPage tabPage3;
        private Sunny.UI.UIButton uiButton4;
        private Sunny.UI.UITextBox uiTextBox3;
        private Sunny.UI.UILabel uiLabel3;
        private Sunny.UI.UIButton uiButton3;
        private Sunny.UI.UITextBox uiTextBox2;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UIButton uiButton2;
        private Sunny.UI.UIButton uiButton5;
        private Sunny.UI.UIButton uiButton6;
        private Sunny.UI.UIButton uiButton7;
    }
}
