﻿using System.Text;
using Wisdom.Utils.Driver;

namespace XianAn.Driver.CameraEWM
{
    public class CameraEWMDriver: DriverBase
    {
        public CameraEWMDriver(string name) : base(name, new CameraEWMProtocal())
        {
            //GetUpProtocol<UcsUpProtocol>().OnReceive += Driver_OnReceive;
        }

        /// <summary>
        /// 拍照
        /// </summary>
        /// <param name="value">刻码信息</param>
        /// <returns>拍照结论</returns>
        public bool TakePicture(out string value,out string limit)
        {
            value = null;
            limit = null;
            try
            {
                string data = "LON";
                var state = Call(data);
                var re = Encoding.ASCII.GetString(state.Data);
                Logger.Info($"数据转换{re}");
                if (re.Contains("ER")) 
                {
                    return false;
                }
                var results = re.Split(':');
                if (results.Count() != 2)
                {
                    return false;
                }
                value = results[0].Trim('\n').Trim('\r');
                limit = results[1];
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);
                return false;
            }
        }
    }
}
