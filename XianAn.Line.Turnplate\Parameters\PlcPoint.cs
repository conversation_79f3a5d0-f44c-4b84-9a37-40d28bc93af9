﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XianAn.Line.Turnplate.Parameters
{
    public static class PlcPoint
    {
        #region 产品编号

        /// <summary>
        /// 视觉前高度检测产品编号地址
        /// </summary>
        public static int sjqgdjccpbh { get; set; } = 308;
        /// <summary>
        /// 耐压产品编号地址
        /// </summary>
        public static int nycpbh { get; set; } = 312;
        /// <summary>
        /// 外观检测产品编号地址
        /// </summary>
        public static int wgjccpbh { get; set; } = 314;
        /// <summary>
        /// 激光刻码产品编号地址
        /// </summary>
        public static int jgkmcpbh { get; set; } = 316;
        /// <summary>
        /// 二维码读取产品编号地址
        /// </summary>
        public static int ewmcpbh { get; set; } = 318;
        /// <summary>
        /// 高度检测产品编号地址
        /// </summary>
        public static int gdjccpbh { get; set; } = 320;
        /// <summary>
        /// 同轴度检测产品编号地址
        /// </summary>
        public static int tzdjccpbh { get; set; } = 322;
        /// <summary>
        /// 下料产品编号地址
        /// </summary>
        public static int xlcpbh { get; set; } = 324;

        #endregion

        #region 到位信号

        /// <summary>
        /// 视觉前高度检测到位地址
        /// 值为1时，上位机读取基恩士电感笔数值，完成后将此地址归0
        /// </summary>
        public static int sjqgdjcdw { get; set; } = 20;
        /// <summary>
        /// 耐压到位地址
        /// 非0时，上位机应触发基恩士相机程序，完成后将此地址归0
        /// </summary>
        public static int nydw { get; set; } = 22;
        /// <summary>
        /// 外观检测到位地址
        /// 非0时，上位机应触发基恩士相机程序，完成后将此地址归0
        /// </summary>
        public static int wgjcdw { get; set; } = 23;
        /// <summary>
        /// 激光刻码到位地址
        /// 值为1时，上位机触发激光刻码，完成后将此地址归0
        /// </summary>
        public static int jgkmdw { get; set; } = 24;
        /// <summary>
        /// 二维码读取到位地址
        /// 值为1时，上位机触发基恩士扫码，完成后将此地址归0
        /// </summary>
        public static int ewmdw { get; set; } = 25;
        /// <summary>
        /// 高度检测到位地址
        /// 值为1时，上位机读取基恩士电感笔数值，完成后将此地址归0
        /// </summary>
        public static int gdjcdw { get; set; } = 26;

        /// <summary>
        /// 高度标准复位地址（G0,250）
        /// 值为1时，上位机复位高度标准，完成后将此地址归0
        /// </summary>
        public static int gdfwdz1 { get; set; } = 275;
        /// <summary>
        /// 高度标准复位地址（G0,150）
        /// 值为1时，上位机复位高度标准，完成后将此地址归0
        /// </summary>
        public static int gdfwdz2 { get; set; } = 276;
        /// <summary>
        /// 高度标准复位地址（G6,250）
        /// 值为1时，上位机复位高度标准，完成后将此地址归0
        /// </summary>
        public static int gdfwdz3 { get; set; } = 277;
        /// <summary>
        /// 高度标准复位地址（G6,150）
        /// 值为1时，上位机复位高度标准，完成后将此地址归0
        /// </summary>
        public static int gdfwdz4 { get; set; } = 278;

        /// <summary>
        /// 同轴度检测到位地址
        /// 值为1时，纪录当前编号同轴度为OK。其他（0除外）值纪录位NG。完成后将此地址归0
        /// </summary>
        public static int tzdjcdw { get; set; } = 27;
        /// <summary>
        /// 下料到位地址
        /// 值为1时，将当前编号产品总结归档，完成后将此地址归0
        /// </summary>
        public static int xldw { get; set; } = 28;

        #endregion

        #region 结论

        /// <summary>
        /// 视觉前高度检测结论地址
        /// </summary>
        public static int sjqgdjcjl { get; set; } = 30;
        /// <summary>
        /// 耐压结论地址
        /// </summary>
        public static int nyjl { get; set; } = 32;
        /// <summary>
        /// 外观检测结论地址
        /// </summary>
        public static int wgjcjl { get; set; } = 33;
        /// <summary>
        /// 激光刻码结论地址
        /// </summary>
        public static int jgkmjl { get; set; } = 34;
        /// <summary>
        /// 二维码读取结论地址
        /// </summary>
        public static int ewmjl { get; set; } = 35;
        /// <summary>
        /// 高度检测结论地址
        /// </summary>
        public static int gdjcjl { get; set; } = 36;
        /// <summary>
        /// 同轴度检测结论地址
        /// </summary>
        public static int tzdjcjl { get; set; } = 37;
        /// <summary>
        /// 下料结论地址
        /// </summary>
        public static int xljl { get; set; } = 38;

        #endregion

        /// <summary>
        /// 编号清零
        /// </summary>
        public static int bhql { get; set; } = 330;

        /// <summary>
        /// 物料编号
        /// </summary>
        public static int MaterielNo { get; set; } = 40;
        /// <summary>
        /// 物料编号更新
        /// </summary>
        public static int UpdateMaterielNo { get; set; } = 41;
    }
}
