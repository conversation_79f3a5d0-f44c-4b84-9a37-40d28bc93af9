﻿using MiniExcelLibs;
using NewLife.Data;
using NewLife.IO;
using NLog;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using XianAn.Line.Turnplate.UnityHelper.Parameters;

namespace XianAn.Line.Turnplate.UnityHelper
{
    public class FileHelper
    {
        /// <summary>
        /// 日志
        /// </summary>
        private Logger Log = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 文件转移
        /// </summary>
        /// <param name="FromPath">文件所在</param>
        /// <param name="DirectoryPath">文件保存</param>
        /// <returns></returns>
        public bool MoveFile(string FromPath, string DirectoryPath)
        {
            try
            {
                string getfilePath = AppDomain.CurrentDomain.BaseDirectory + "OriginalRecord\\Results";

                string[] picList = Directory.GetFiles(FromPath, "*.csv");

                if (picList.Count() == 0)
                    return false;
                foreach (string f in picList)
                {
                    try
                    {
                        //取得文件名.
                        string fName = f.Substring(FromPath.Length + 1);
                        if (DirectoryPath != null)
                        {
                            File.Copy(Path.Combine(FromPath, fName), Path.Combine(DirectoryPath, fName), true);
                        }
                        else
                        {
                            File.Copy(Path.Combine(FromPath, fName), Path.Combine(getfilePath, fName), true);
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex);
                    }
                }

                //删除原始文件夹里的文件
                foreach (string f in picList)
                {
                    File.Delete(f);
                }
                return true;
            }
            catch (DirectoryNotFoundException dirNotFound)
            {
                Log.Error(dirNotFound, dirNotFound.Message);
                //MessageBox.Show(dirNotFound.Message);
                return false;
            }
        }
        //删除目录下所有的文件
        public bool DeleteFile(string path)
        {
            try
            {
                if (Directory.Exists(path))
                {
                    foreach (string f in Directory.GetFileSystemEntries(path))
                    {
                        if (File.Exists(f))
                        {
                            //删除文件
                            File.Delete(f);
                        }
                        else
                        {
                            //循环删除文件夹
                            //DeleteFile(f);
                        }
                    }
                    //Directory.Delete(path);
                }
                return true;
            }
            catch (DirectoryNotFoundException dirNotFound)
            {
                Log.Error(dirNotFound, dirNotFound.Message);
                //MessageBox.Show(dirNotFound.Message);
                return false;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        /// <summary>
        /// 获取数据
        /// </summary>
        /// <param name="sendData">下发数据</param>
        public bool GetMeasuredValues(string? sendData, string fileType, string yzjLimit1, string yzjLimit2, out UpFileData value)
        {
            value = null;
            try
            {
                if (sendData == null)
                {
                    return false;
                }
                var datas = sendData.Split(',');
                if (datas.Any())
                {
                    if (fileType == "1")
                    {
                        value = new UpFileData();

                        value.CircleDiameter1 = datas[3];

                        value.CheckBit = datas[2];

                        var limitList = yzjLimit1.Split(",");
                        if (limitList.Count() == 2)
                        {
                            var limitMax = double.Parse(limitList[0]);
                            var limitMin = double.Parse(limitList[1]);
                            var data = double.Parse(datas[3]);
                            if (data <= limitMax && data >= limitMin)
                            {
                                value.CircleDiameterRes1 = "true";
                            }
                            else
                            {
                                value.CircleDiameterRes1 = "false";
                            }
                        }

                        if (datas[4] == "1")
                        {
                            value.MBResult = "true";
                        }
                        else
                        {
                            value.MBResult = "false";
                        }

                        if (datas[0] == "1" && datas[1] == "0")
                        {
                            return true;
                        }
                        else
                        {
                            return false;
                        }

                    }
                    else if (fileType == "2")
                    {
                        value = new UpFileData();

                        value.CircleDiameter1 = datas[3];

                        value.CheckBit = datas[2];

                        var limitList = yzjLimit1.Split(",");
                        if (limitList.Count() == 2)
                        {
                            var limitMax = double.Parse(limitList[0]);
                            var limitMin = double.Parse(limitList[1]);
                            var data = double.Parse(datas[3]);
                            if (data <= limitMax && data >= limitMin)
                            {
                                value.CircleDiameterRes1 = "true";
                            }
                            else
                            {
                                value.CircleDiameterRes1 = "false";
                            }
                        }

                        if (datas[4] == "1")
                        {
                            value.MBResult = "true";
                        }
                        else
                        {
                            value.MBResult = "false";
                        }

                        if (datas[0] == "1" && datas[1] == "0")
                        {
                            return true;
                        }
                        else
                        {
                            return false;
                        }
                    }
                    else if (fileType == "3")
                    {
                        value = new UpFileData();

                        value.CircleDiameter1 = datas[2];

                        var limitList = yzjLimit1.Split(",");
                        if (limitList.Count() == 2)
                        {
                            var limitMax = double.Parse(limitList[0]);
                            var limitMin = double.Parse(limitList[1]);
                            var data = double.Parse(datas[2]);
                            if (data <= limitMax && data >= limitMin)
                            {
                                value.CircleDiameterRes1 = "true";
                            }
                            else
                            {
                                value.CircleDiameterRes1 = "false";
                            }
                        }

                        if (datas[3] == "1")
                        {
                            value.MBResult = "true";
                        }
                        else
                        {
                            value.MBResult = "false";
                        }

                        if (datas[0] == "1" && datas[1] == "0")
                        {
                            return true;
                        }
                        else
                        {
                            return false;
                        }
                    }
                    else
                    {
                        return false;
                    }
                    
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                return false;
                //MessageBox.Show(ex.Message);
            }
        }
        /// <summary>
        /// 获取数据
        /// </summary>
        /// <param name="sendData">下发数据</param>
        public bool GetMeasuredValues(string? sendData, string fileType,string yzjLimit1, string yzjLimit2, out DownFileData value)
        {
            value = null;
            try
            {
                if (sendData == null)
                {
                    return false;
                }
                var datas = sendData.Split(',');
                if (datas.Any())
                {
                    if (fileType == "1")
                    {
                        value = new DownFileData();
                        value.CircleDiameter2 = datas[2];

                        var limitList = yzjLimit2.Split(",");
                        if (limitList.Count() == 2) 
                        {
                            var limitMax = double.Parse(limitList[0]);
                            var limitMin = double.Parse(limitList[1]);
                            var data = double.Parse(datas[2]);
                            if (data <= limitMax && data >= limitMin)
                            {
                                value.CircleDiameterRes2 = "true";
                            }
                            else 
                            {
                                value.CircleDiameterRes2 = "false";
                            }
                        }

                        if (datas[3] == "1")
                        {
                            value.MBResult = "true";
                        }
                        else 
                        {
                            value.MBResult = "false";
                        }

                        if (datas[0] == "1" && datas[1] == "0")
                        {
                            return true;
                        }
                        else
                        {
                            return false;
                        }
                    }
                    else if (fileType == "2")
                    {
                        value = new DownFileData();
                        value.CircleDiameter2 = datas[2];

                        var limitList = yzjLimit2.Split(",");
                        if (limitList.Count() == 2)
                        {
                            var limitMax = double.Parse(limitList[0]);
                            var limitMin = double.Parse(limitList[1]);
                            var data = double.Parse(datas[2]);
                            if (data <= limitMax && data >= limitMin)
                            {
                                value.CircleDiameterRes2 = "true";
                            }
                            else
                            {
                                value.CircleDiameterRes2 = "false";
                            }
                        }

                        if (datas[3] == "1")
                        {
                            value.MBResult = "true";
                        }
                        else
                        {
                            value.MBResult = "false";
                        }

                        if (datas[0] == "1" && datas[1] == "0")
                        {
                            return true;
                        }
                        else
                        {
                            return false;
                        }
                    }
                    else if (fileType == "3")
                    {
                        value = new DownFileData();
                        value.CircleDiameter2 = datas[3];
                        value.CheckBit = datas[2];

                        var limitList = yzjLimit2.Split(",");
                        if (limitList.Count() == 2)
                        {
                            var limitMax = double.Parse(limitList[0]);
                            var limitMin = double.Parse(limitList[1]);
                            var data = double.Parse(datas[3]);
                            if (data <= limitMax && data >= limitMin)
                            {
                                value.CircleDiameterRes2 = "true";
                            }
                            else
                            {
                                value.CircleDiameterRes2 = "false";
                            }
                        }

                        if (datas[4] == "1")
                        {
                            value.MBResult = "true";
                        }
                        else
                        {
                            value.MBResult = "false";
                        }

                        if (datas[0] == "1" && datas[1] == "0")
                        {
                            return true;
                        }
                        else
                        {
                            return false;
                        }
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                return false;
                //MessageBox.Show(ex.Message);
            }
        }
        /// <summary>
        /// 获取数据
        /// </summary>
        /// <param name="sendData">下发数据</param>
        public bool GetMeasuredValues(string? sendData, out string value)
        {
            value = null;
            try
            {
                if (sendData == null)
                {
                    return false;
                }
                var datas = sendData.Split(',');
                if (datas.Any())
                {
                    if (datas[2] == "OK")
                    {
                        value = datas[9].Substring(0, 1);
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                return false;
                //MessageBox.Show(ex.Message);
            }
        }
        /// <summary>
        /// 保存文件夹下数据
        /// </summary>
        /// <param name="value"></param>
        public bool SaveMeasuredValues(string? savePath, string fileName, SaveFileData value)
        {
            try
            {
                if (savePath == null)
                {
                    return false;
                }
                string[] picLists = Directory.GetFiles(savePath);
                if (picLists.Any())
                {
                    bool fileExists = picLists.Any(file => Path.GetFileName(file).Equals(fileName, StringComparison.OrdinalIgnoreCase));
                    if (fileExists)
                    {
                        // 获取属性描述
                        var type = typeof(SaveFileData);
                        var properties = type.GetProperties();

                        var headers = properties.Select(p =>
                        {
                            var description = p.GetCustomAttributes(typeof(DescriptionAttribute), false)
                                               .Cast<DescriptionAttribute>()
                                               .FirstOrDefault();
                            return description != null ? description.Description : p.Name;
                        }).ToArray();
                        // 获取数据行（属性值）
                        var rowData = properties.Select(p => p.GetValue(value)?.ToString()).ToArray();
                        // 将表头和数据行组合成二维数组
                        var saveData = new List<Dictionary<string, object>>();
                        var dic = new Dictionary<string, object>();
                        // 定义 Excel 文件路径
                        string filePath = savePath + "\\" + fileName;

                        // 读取现有数据（包括表头）
                        var oldDatas = MiniExcel.Query(filePath).ToList();
                        oldDatas.Remove(oldDatas.FirstOrDefault());
                        foreach (var oldData in oldDatas)
                        {
                            int i = 0;
                            dic = new Dictionary<string, object>();
                            foreach (var item in oldData)
                            {
                                dic.Add(headers[i], item.Value.ToString());
                                i++;
                            }
                            saveData.Add(dic);
                        }

                        dic = new Dictionary<string, object>();

                        for (var i = 0; i < headers.Count(); i++)
                        {
                            dic.Add(headers[i], rowData[i]);
                        }
                        saveData.Add(dic);
                        //删除源文件
                        File.Delete(filePath);
                        // 保存更新后的数据到 CSV 文件
                        MiniExcel.SaveAs(filePath, saveData);
                    }
                    else
                    {
                        // 获取属性描述
                        var type = typeof(SaveFileData);
                        var properties = type.GetProperties();

                        var headers = properties.Select(p =>
                        {
                            var description = p.GetCustomAttributes(typeof(DescriptionAttribute), false)
                                               .Cast<DescriptionAttribute>()
                                               .FirstOrDefault();
                            return description != null ? description.Description : p.Name;
                        }).ToArray();
                        // 获取数据行（属性值）
                        var rowData = properties.Select(p => p.GetValue(value)?.ToString()).ToArray();
                        // 将表头和数据行组合成二维数组
                        var saveData = new List<Dictionary<string, object>>();
                        var dic = new Dictionary<string, object>();
                        for (var i = 0; i < headers.Count(); i++)
                        {
                            dic.Add(headers[i], rowData[i]);
                        }
                        saveData.Add(dic);
                        // 保存到 Excel 文件
                        var path = savePath + "\\" + fileName;
                        MiniExcel.SaveAs(path, saveData, overwriteFile: true);
                    }
                    return true;
                }
                else
                {
                    // 获取属性描述
                    var type = typeof(SaveFileData);
                    var properties = type.GetProperties();

                    var headers = properties.Select(p =>
                    {
                        var description = p.GetCustomAttributes(typeof(DescriptionAttribute), false)
                                           .Cast<DescriptionAttribute>()
                                           .FirstOrDefault();
                        return description != null ? description.Description : p.Name;
                    }).ToArray();
                    // 获取数据行（属性值）
                    var rowData = properties.Select(p => p.GetValue(value)?.ToString()).ToArray();
                    // 将表头和数据行组合成二维数组
                    var saveData = new List<Dictionary<string, object>>();
                    var dic = new Dictionary<string, object>();
                    for (var i = 0; i < headers.Count(); i++)
                    {
                        dic.Add(headers[i], rowData[i]);
                    }
                    saveData.Add(dic);
                    // 保存到 Excel 文件
                    var path = savePath + "\\" + fileName;
                    MiniExcel.SaveAs(path, saveData, overwriteFile: true);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                return false;
                //MessageBox.Show(ex.Message);
            }
        }
    }
}
