using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.VisualBasic.Logging;
using NewLife.Data;
using NLog;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Net.WebSockets;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Wisdom.Utils;
using Wisdom.Utils.Driver;
using Wisdom.Utils.Driver.Arg;
using Wisdom.Utils.Driver.Checker;
using XianA.Driver.EtchingMachine;
using XianA.Driver.EtchingMachineJPT;
using XianA.Driver.EtchingMachineJPT.Parameters;
using XianAn.Driver.CameraEWM;
using XianAn.Driver.CameraJNS;
using XianAn.Driver.HeightCheck;
using XianAn.Driver.NY;
using XianAn.Driver.PLC;
using XianAn.Line.Turnplate.Dal;
using XianAn.Line.Turnplate.Dal.Model;
using XianAn.Line.Turnplate.Parameters;
using XianAn.Line.Turnplate.UnityHelper;
using XianAn.Line.Turnplate.UnityHelper.Parameters;
using static System.Net.Mime.MediaTypeNames;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Button;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.ToolTip;

namespace XianAn.Line.Turnplate
{
    public partial class MainControl : UIPage
    {
        /// <summary>
        /// 视觉前高度检测
        /// </summary>
        private Repeater SJQGDJCRepeater;
        // <summary>
        /// 耐压检测
        /// </summary>
        private Repeater NYJCRepeater;
        /// <summary>
        /// 外观检查轮询
        /// </summary>
        private Repeater WGJCRepeater;
        /// <summary>
        /// 模板切换轮询
        /// </summary>
        private Repeater MBQHRepeater;
        /// <summary>
        /// 蚀刻机轮询
        /// </summary>
        private Repeater SKJRepeater;
        /// <summary>
        /// 二维码检测轮询
        /// </summary>
        private Repeater EWMJCRepeater;
        /// <summary>
        /// 高度检测
        /// </summary>
        private Repeater GDJCRepeater;
        /// <summary>
        /// 同轴度检测
        /// </summary>
        private Repeater TZDRepeater;
        /// <summary>
        /// 下料
        /// </summary>
        private Repeater XLRepeater;
        /// <summary>
        /// 高度标准复位
        /// </summary>
        private Repeater GDBZRepeater;
        /// <summary>
        /// 激光刻码
        /// </summary>
        private EMDriver emDriver;
        /// <summary>
        /// 二维码相机
        /// </summary>
        private CameraEWMDriver ewmDriver;
        ///<summary>
        /// 外观检测相机
        /// </summary>
        private CameraDriver cameraDriver;
        ///<summary>
        /// 标识符检测相机
        /// </summary>
        private CameraDriver markCameraDriver;
        ///<summary>
        /// 高度检测
        /// </summary>
        private HeightCheckDriver heightCheckDriver;
        ///<summary>
        /// 视觉前高度检测
        /// </summary>
        private HeightCheckDriver heightCheckDriverSJQ;
        /// <summary>
        /// plc
        /// </summary>
        private PlcDriver plcDriver;
        /// <summary>
        /// JPT刻码机
        /// </summary>
        //public JPTDriver jptDriver;
        /// <summary>
        /// 耐压仪
        /// </summary>
        public NYDriver nyDriver;
        /// <summary>
        /// 日志
        /// </summary>
        private Logger logger = LogManager.GetCurrentClassLogger();
        /// <summary>
        /// 配置文件
        /// </summary>
        private RunConSetting runConSetting = RunConSetting.Current;
        /// <summary>
        /// 文件读取
        /// </summary>
        private FileHelper fileHelper = new FileHelper();
        /// <summary>
        /// 日志打印
        /// </summary>
        RichTextAppendHelper _richTextAppendHelper;
        /// <summary>
        /// 耐压参数设置
        /// </summary>
        public NYSetData nySetData = null;
        /// <summary>
        /// 耐压参数设置集合
        /// </summary>
        public List<NYSetData> nySetDatas = new List<NYSetData>();
        /// <summary>
        /// 二维码等级
        /// </summary>
        public List<string> characters = new List<string> { "A", "B", "C", "D", "E", "F" };
        /// <summary>
        /// 模板切换锁
        /// </summary>
        public readonly object mbqhObject = new object();
        /// <summary>
        /// 数据保存锁
        /// </summary>
        public readonly object dataSaveObject = new object();

        public MainControl()
        {
            InitializeComponent();

            //jptDriver = new JPTDriver();
            //jptDriver.InitService(runConSetting.EtchingMachineIP, runConSetting.EtchingMachinePort);
            //jptDriver.GetMarkingParamEvent += LaserEngraving;
            //jptDriver.SendDataFinishEvent += LaserEngravingFinish;

            emDriver = new EMDriver("激光刻码");
            heightCheckDriverSJQ = new HeightCheckDriver("视觉前高度检测");
            nyDriver = new NYDriver(runConSetting.NYYPort);
            ewmDriver = new CameraEWMDriver("二维码相机");
            cameraDriver = new CameraDriver("外观检测相机");
            markCameraDriver = new CameraDriver("标识符检测相机");
            heightCheckDriver = new HeightCheckDriver("高度检测");
            plcDriver = new PlcDriver(runConSetting.PlcIP, runConSetting.PlcPort);
            _richTextAppendHelper = new RichTextAppendHelper(this.uiRichTextBox1);
            InitDriver();
            InitDataGridView1();
            InitSystemControl();
            DataPresentation();
            InitButton();

            //test();
            //plcDriver = new PlcDriver("127.0.0.1", 502);
            //test2();
            SJQGDJCRepeater = new Repeater(TimeSpan.FromSeconds(0.5), HeightDetectionSJQ, 1000);
            NYJCRepeater = new Repeater(TimeSpan.FromSeconds(0.5), InsulationResistance, 1000);
            WGJCRepeater = new Repeater(TimeSpan.FromSeconds(0.1), AppearanceDetection, 1000);
            MBQHRepeater = new Repeater(TimeSpan.FromSeconds(1), ModelChange, 1000);
            SKJRepeater = new Repeater(TimeSpan.FromSeconds(1), LaserEngraving, 1000);
            EWMJCRepeater = new Repeater(TimeSpan.FromSeconds(1), TDCodeDetection, 1000);
            GDJCRepeater = new Repeater(TimeSpan.FromSeconds(0.5), HeightDetection, 1000);
            TZDRepeater = new Repeater(TimeSpan.FromSeconds(1), CoaxialityDetection, 1000);
            XLRepeater = new Repeater(TimeSpan.FromSeconds(1), FinishedBlanking, 1000);
            GDBZRepeater = new Repeater(TimeSpan.FromSeconds(0.5), ReHeightStandard, 1000);

            SJQGDJCRepeater.Start();
            NYJCRepeater.Start();
            WGJCRepeater.Start();
            MBQHRepeater.Start();
            SKJRepeater.Start();
            EWMJCRepeater.Start();
            GDJCRepeater.Start();
            TZDRepeater.Start();
            XLRepeater.Start();
            GDBZRepeater.Start();
        }

        /// <summary>
        /// 初始化驱动
        /// </summary>
        private void InitDriver()
        {
            try
            {
                //视觉前高度检测
                var arg1 = new NetArg(runConSetting.HeightCheckIPSJQ, runConSetting.HeightCheckPortSJQ);
                heightCheckDriverSJQ.Connect(arg1);
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }

            try
            {
                //耐压仪
                nyDriver.Connect();
                //nyDriver.ReadFloat2(1);
                //nyDriver.ReadData1();
                //nyDriver.ReadFloat2(0x3005);
                //nyDriver.WriteFloat(16437, 2000000);
                //nyDriver.ReadFloat(16437);
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }

            try
            {
                //激光刻码
                var arg1 = new NetArg(runConSetting.EtchingMachineIP, runConSetting.EtchingMachinePort);
                emDriver.Connect(arg1);
                //emDriver.Disconnect();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
            try
            {
                //二维码相机
                var arg2 = new NetArg(runConSetting.CameraEWMIP, runConSetting.CameraEWMPort);
                ewmDriver.Connect(arg2);
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
            try
            {
                //外观检测相机
                var arg3 = new NetArg(runConSetting.CameraWGJCIP, runConSetting.CameraWGJCPort);
                cameraDriver.Connect(arg3);
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
            try
            {
                //标识符检测相机
                var arg5 = new NetArg(runConSetting.CameraBSFJCIP, runConSetting.CameraBSFJCPort);
                markCameraDriver.Connect(arg5);
                //拍照预设值
                var yszres = markCameraDriver.PresetValue();
                if (!yszres)
                {
                    logger.Error("标识符拍照预设值失败");
                    LogSafeInvokeAppend("3", "标识符拍照预设值失败");
                }
                Task.Delay(TimeSpan.FromSeconds(5)).Wait();
                if (markCameraDriver.FinishYSZ)
                {
                    logger.Info("标识符拍照预设值成功");
                }
                else
                {
                    logger.Error("标识符拍照预设值失败");
                    LogSafeInvokeAppend("3", "标识符拍照预设值失败");
                }

            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
            try
            {
                //高度检测
                var arg4 = new NetArg(runConSetting.HeightCheckIP, runConSetting.HeightCheckPort);
                heightCheckDriver.Connect(arg4);
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
            try
            {
                //plc
                plcDriver.Connect();
                //下发模板
                using (var db = new MyDbContext())
                {
                    //获取样品类别
                    var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                    var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                    if (testindex == null)
                    {
                        logger.Error($"外观检查模板切换获取物料编号{indexno}信息异常");
                        LogSafeInvokeAppend("3", $"外观检查模板切换获取物料编号{indexno}信息异常");
                    }

                    var res = cameraDriver.SetModel(testindex.ModelChangeCommand);
                    if (!res)
                    {
                        logger.Error($"外观检查模板切换获取物料编号{indexno}模板失败");
                        LogSafeInvokeAppend("3", $"外观检查模板切换获取物料编号{indexno}模板失败");
                        return;
                    }
                    Task.Delay(TimeSpan.FromSeconds(8)).Wait();
                    if (!cameraDriver.FinishQHMB)
                    {
                        logger.Error($"物料编号{indexno}模板失败");
                        LogSafeInvokeAppend("3", $"物料编号{indexno}模板失败");
                    }
                    else
                    {
                        logger.Info($"物料编号{indexno}模板切换成功");
                    }
                }
                var historyDate = runConSetting.HistoryDateTime;
                var datetimeNow = DateTime.Now.ToString("yyyyMMdd");
                if (historyDate != datetimeNow)
                {
                    //编号清零
                    var plcre = plcDriver.WriteShort(PlcPoint.bhql, 1);
                    if (plcre)
                    {
                        logger.Info($"编号清零成功");
                        runConSetting.HistoryDateTime = datetimeNow;
                        runConSetting.Save();
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
        }

        /// <summary>
        /// 外观检查
        /// </summary>
        private void AppearanceDetection()
        {
            try
            {
                //获取到位信号
                var state = plcDriver.ReadShort(PlcPoint.wgjcdw);
                if (state == -1)
                {
                    logger.Error("外观检测获取到位信号异常");
                    LogSafeInvokeAppend("3", "外观检测获取到位信号异常");
                    return;
                }
                else if (state == 0)
                {
                    return;
                }
                else if (state > 50)
                {
                    logger.Error("外观检测与plc连接失败");
                    LogSafeInvokeAppend("3", "外观检测与plc连接失败");
                    return;
                }
                else
                {
                    lock (mbqhObject)
                    {
                        logger.Info($"外观检测获取到位信号{state}");
                        //外观检测
                        if (state == 1)
                        {
                            //连接数据库
                            using (var db = new MyDbContext())
                            {
                                //获取样品类别
                                var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                                var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                                if (testindex == null)
                                {
                                    logger.Error($"外观检测获取物料编号{indexno}信息异常");
                                    LogSafeInvokeAppend("3", $"外观检测获取物料编号{indexno}信息异常");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }
                                //切换相机方案
                                var setres = cameraDriver.SetAction(testindex.UpPhotoCommand);
                                logger.Info($"外观检测设置正面拍照方案{testindex.UpPhotoCommand}");
                                if (!setres)
                                {
                                    logger.Error("设置正面拍照方案失败");
                                    LogSafeInvokeAppend("3", "设置正面拍照方案失败");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }

                                try
                                {
                                    TaskHelper.WaitUntil(() => cameraDriver.FinishQH, 25, 3000).Wait();
                                }
                                catch (Exception ex)
                                {
                                    logger.Error("正面拍照方案切换失败2");
                                    LogSafeInvokeAppend("3", "正面拍照方案切换失败2");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }

                                //Task.Delay(TimeSpan.FromSeconds(1.5)).Wait();
                                //if (!cameraDriver.FinishQH)
                                //{
                                //    logger.Error("反面拍照方案切换失败2");
                                //    LogSafeInvokeAppend("3", "反面拍照方案切换失败2");
                                //    //下发plc结论
                                //    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                //    //下发plc试验完成
                                //    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                //    return;
                                //}
                                //复位
                                var plcre2 = plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                            }
                        }
                        else if (state == 2)
                        {
                            //连接数据库
                            using (var db = new MyDbContext())
                            {
                                //获取样品类别
                                var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                                var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                                if (testindex == null)
                                {
                                    logger.Error($"外观检测获取物料编号{indexno}信息异常");
                                    LogSafeInvokeAppend("3", $"外观检测获取物料编号{indexno}信息异常");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }
                                //获取当前产品编号
                                var no = plcDriver.ReadInt(PlcPoint.wgjccpbh);
                                if (no == -1)
                                {
                                    logger.Error($"外观检测获取产品编号异常{no}");
                                    LogSafeInvokeAppend("3", $"外观检测获取产品编号异常{no}");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }
                                //拍照
                                var pzres = cameraDriver.TakePicture();
                                if (!pzres)
                                {
                                    logger.Error("正面拍照失败");
                                    LogSafeInvokeAppend("3", "正面拍照失败");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }
                                try
                                {
                                    TaskHelper.WaitUntil(() => cameraDriver.FinishPZ, 25, 3000).Wait();
                                }
                                catch (Exception ex)
                                {
                                    logger.Error("正面拍照返回失败");
                                    LogSafeInvokeAppend("3", "正面拍照返回失败");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }

                                //Task.Delay(TimeSpan.FromSeconds(1.5)).Wait();
                                //if (!cameraDriver.FinishPZ)
                                //{
                                //    logger.Error("反面拍照返回失败");
                                //    LogSafeInvokeAppend("3", "反面拍照返回失败");
                                //    //下发plc结论
                                //    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                //    //下发plc试验完成
                                //    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                //    return;
                                //}
                                //Task.Delay(TimeSpan.FromSeconds(0.8)).Wait();
                                //var checkre = cameraDriver.CheckResult(cameraDriver.ResultPZ, out string type);
                                //logger.Info($"s上拍照产品编号{no}标识{type}");
                                //if (type == null)
                                //{
                                //    logger.Error("正面拍照返回数据异常");
                                //    LogSafeInvokeAppend("3", "正面拍照返回数据异常");
                                //    return;
                                //}
                                logger.Info($"正面拍照数据{cameraDriver.ResultPZ}");
                                //查询数据
                                var checkre = fileHelper.GetMeasuredValues(cameraDriver.ResultPZ, testindex.FileType, testindex.YZJLimit1, testindex.YZJLimit2, out UpFileData value);
                                if (value == null)
                                {
                                    logger.Error("数据获取异常");
                                    LogSafeInvokeAppend("3", "数据获取异常");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }
                                //默认合格
                                if (runConSetting.PassWGJC == "true")
                                {
                                    checkre = true;
                                }
                                //查询数据是否含有当前数据
                                var testresult = db.Set<TestResult>().FirstOrDefault(x => x.SampleNo == no.ToString() && x.LayOffMark != "1");
                                var testresultfirst7 = db.Set<TestResult>().Where(x => x.SampleNo != no.ToString()).OrderByDescending(x => x.CreateTime).ToList().Take(7).ToList();
                                if (testresult != null)
                                {
                                    if (testindex.FileType == "1")
                                    {
                                        //先判断是否有重复编号
                                        if (runConSetting.CFBHJC == "true")
                                        {
                                            var cf = CheckRepeatData(value.CheckBit, testresultfirst7);
                                            if (cf)
                                            {
                                                logger.Error($"编号{value.CheckBit}存在重复数据");
                                                LogSafeInvokeAppend("3", $"编号{value.CheckBit}存在重复数据");
                                                return;
                                            }
                                        }
                                        testresult.CircleDiameter1 = value.CircleDiameter1;
                                        testresult.CircleDiameterRes1 = runConSetting.PassWGJC == "true" ? "合格" : (value.CircleDiameterRes1 == "true" ? "合格" : "不合格");
                                        testresult.CheckBit = value.CheckBit;
                                        testresult.AppearanceData = value.CheckBit;
                                        testresult.AppearanceResult = checkre ? "合格" : "不合格";
                                        testresult.MBResult = runConSetting.PassWGJC == "true" ? "合格" : (value.MBResult == "true" ? "合格" : "不合格");
                                        lock (dataSaveObject)
                                        {
                                            db.Set<TestResult>().Update(testresult);
                                            db.SaveChanges();
                                        }
                                    }
                                    else if (testindex.FileType == "2")
                                    {
                                        //先判断是否有重复编号
                                        if (runConSetting.CFBHJC == "true")
                                        {
                                            var cf = CheckRepeatData(value.CheckBit, testresultfirst7);
                                            if (cf)
                                            {
                                                logger.Error($"编号{value.CheckBit}存在重复数据");
                                                LogSafeInvokeAppend("3", $"编号{value.CheckBit}存在重复数据");
                                                return;
                                            }
                                        }
                                        testresult.CircleDiameter1 = value.CircleDiameter1;
                                        testresult.CircleDiameterRes1 = runConSetting.PassWGJC == "true" ? "合格" : (value.CircleDiameterRes1 == "true" ? "合格" : "不合格");
                                        testresult.CheckBit = value.CheckBit;
                                        testresult.AppearanceData = value.CheckBit;
                                        testresult.AppearanceResult = checkre ? "合格" : "不合格";
                                        testresult.MBResult = runConSetting.PassWGJC == "true" ? "合格" : (value.MBResult == "true" ? "合格" : "不合格");

                                        lock (dataSaveObject)
                                        {
                                            db.Set<TestResult>().Update(testresult);
                                            db.SaveChanges();
                                        }
                                    }
                                    else if (testindex.FileType == "3")
                                    {
                                        testresult.CircleDiameter1 = value.CircleDiameter1;
                                        testresult.AppearanceResult = checkre ? "合格" : "不合格";
                                        testresult.CircleDiameterRes1 = runConSetting.PassWGJC == "true" ? "合格" : (value.CircleDiameterRes1 == "true" ? "合格" : "不合格");
                                        testresult.MBResult = runConSetting.PassWGJC == "true" ? "合格" : (value.MBResult == "true" ? "合格" : "不合格");
                                        lock (dataSaveObject)
                                        {
                                            db.Set<TestResult>().Update(testresult);
                                            db.SaveChanges();
                                        }
                                    }
                                    else
                                    {
                                        logger.Error("正面拍照csv文件类型异常");
                                        LogSafeInvokeAppend("3", "正面拍照csv文件类型异常");
                                        //下发plc结论
                                        plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                        //下发plc试验完成
                                        plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                        return;
                                    }
                                }
                                else
                                {
                                    if (testindex.FileType == "1")
                                    {
                                        //先判断是否有重复编号
                                        if (runConSetting.CFBHJC == "true")
                                        {
                                            var cf = CheckRepeatData(value.CheckBit, testresultfirst7);
                                            if (cf)
                                            {
                                                logger.Error($"编号{value.CheckBit}存在重复数据");
                                                LogSafeInvokeAppend("3", $"编号{value.CheckBit}存在重复数据");
                                                return;
                                            }
                                        }
                                        var testResultNew = new TestResult()
                                        {
                                            Id = Guid.NewGuid().ToString("N"),
                                            SampleNo = no.ToString(),
                                            CircleDiameter1 = value.CircleDiameter1,
                                            CircleDiameterRes1 = runConSetting.PassWGJC == "true" ? "合格" : (value.CircleDiameterRes1 == "true" ? "合格" : "不合格"),
                                            CheckBit = value.CheckBit,
                                            AppearanceData = value.CheckBit,
                                            AppearanceResult = checkre ? "合格" : "不合格",
                                            MBResult = runConSetting.PassWGJC == "true" ? "合格" : (value.MBResult == "true" ? "合格" : "不合格"),
                                            CreateTime = DateTime.Now,
                                        };
                                        lock (dataSaveObject)
                                        {
                                            db.Set<TestResult>().Add(testResultNew);
                                            db.SaveChanges();
                                        }
                                    }
                                    else if (testindex.FileType == "2")
                                    {
                                        //先判断是否有重复编号
                                        if (runConSetting.CFBHJC == "true")
                                        {
                                            var cf = CheckRepeatData(value.CheckBit, testresultfirst7);
                                            if (cf)
                                            {
                                                logger.Error($"编号{value.CheckBit}存在重复数据");
                                                LogSafeInvokeAppend("3", $"编号{value.CheckBit}存在重复数据");
                                                return;
                                            }
                                        }
                                        var testResultNew = new TestResult()
                                        {
                                            Id = Guid.NewGuid().ToString("N"),
                                            SampleNo = no.ToString(),
                                            CircleDiameter1 = value.CircleDiameter1,
                                            CircleDiameterRes1 = runConSetting.PassWGJC == "true" ? "合格" : (value.CircleDiameterRes1 == "true" ? "合格" : "不合格"),
                                            CheckBit = value.CheckBit,
                                            AppearanceData = value.CheckBit,
                                            AppearanceResult = checkre ? "合格" : "不合格",
                                            MBResult = runConSetting.PassWGJC == "true" ? "合格" : (value.MBResult == "true" ? "合格" : "不合格"),
                                            CreateTime = DateTime.Now,
                                        };
                                        lock (dataSaveObject)
                                        {
                                            db.Set<TestResult>().Add(testResultNew);
                                            db.SaveChanges();
                                        }
                                    }
                                    else if (testindex.FileType == "3")
                                    {
                                        var testResultNew = new TestResult()
                                        {
                                            Id = Guid.NewGuid().ToString("N"),
                                            SampleNo = no.ToString(),
                                            CircleDiameter1 = value.CircleDiameter1,
                                            CircleDiameterRes1 = runConSetting.PassWGJC == "true" ? "合格" : (value.CircleDiameterRes1 == "true" ? "合格" : "不合格"),
                                            AppearanceResult = checkre ? "合格" : "不合格",
                                            MBResult = runConSetting.PassWGJC == "true" ? "合格" : (value.MBResult == "true" ? "合格" : "不合格"),
                                            CreateTime = DateTime.Now,
                                        };
                                        lock (dataSaveObject)
                                        {
                                            db.Set<TestResult>().Add(testResultNew);
                                            db.SaveChanges();
                                        }
                                    }
                                    else
                                    {
                                        logger.Error("正面拍照csv文件类型异常");
                                        LogSafeInvokeAppend("3", "正面拍照csv文件类型异常");
                                        //下发plc结论
                                        plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                        //下发plc试验完成
                                        plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                        return;
                                    }
                                }
                                //下发plc结论
                                ushort resultWgjc = checkre ? (ushort)1 : (ushort)2;
                                var plcre1 = plcDriver.WriteShort(PlcPoint.wgjcjl, resultWgjc);
                                //下发plc试验完成
                                var plcre2 = plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                //移除数据文件
                                //string getfilePath = AppDomain.CurrentDomain.BaseDirectory + "OriginalRecord\\Results";
                                //var ycjl = fileHelper.MoveFile(runConSetting.UpPictureFileAdd, getfilePath);
                                //if (!ycjl)
                                //{
                                //    logger.Error("上拍照移除文件失败");
                                //}
                            }
                        }
                        else if (state == 3)
                        {
                            //连接数据库
                            using (var db = new MyDbContext())
                            {
                                //获取样品类别
                                var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                                var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                                if (testindex == null)
                                {
                                    logger.Error($"外观检测获取物料编号{indexno}信息异常");
                                    LogSafeInvokeAppend("3", $"外观检测获取物料编号{indexno}信息异常");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }
                                //切换相机方案
                                var setres = cameraDriver.SetAction(testindex.DownPhotoCommand);
                                logger.Info($"外观检测设置反面拍照方案{testindex.DownPhotoCommand}");
                                if (!setres)
                                {
                                    logger.Error("设置反面拍照方案失败");
                                    LogSafeInvokeAppend("3", "设置反面拍照方案失败");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }

                                try
                                {
                                    TaskHelper.WaitUntil(() => cameraDriver.FinishQH, 25, 3000).Wait();
                                }
                                catch (Exception ex)
                                {
                                    logger.Error("反面拍照方案切换失败2");
                                    LogSafeInvokeAppend("3", "反面拍照方案切换失败2");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }

                                //Task.Delay(TimeSpan.FromSeconds(1.5)).Wait();
                                //if (!cameraDriver.FinishQH)
                                //{
                                //    logger.Error("正面拍照方案切换失败2");
                                //    LogSafeInvokeAppend("3", "正面拍照方案切换失败2");
                                //    //下发plc结论
                                //    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                //    //下发plc试验完成
                                //    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                //    return;
                                //}
                                //复位
                                var plcre2 = plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                            }
                        }
                        else if (state == 4)
                        {
                            //连接数据库
                            using (var db = new MyDbContext())
                            {
                                //获取样品类别
                                var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                                var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                                if (testindex == null)
                                {
                                    logger.Error($"外观检测获取物料编号{indexno}信息异常");
                                    LogSafeInvokeAppend("3", $"外观检测获取物料编号{indexno}信息异常");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }

                                //获取当前产品编号
                                var no = plcDriver.ReadInt(PlcPoint.wgjccpbh);
                                if (no == -1)
                                {
                                    logger.Error($"外观检测获取产品编号异常{no}");
                                    LogSafeInvokeAppend("3", $"外观检测获取产品编号异常{no}");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }
                                //拍照
                                var pzres = cameraDriver.TakePicture();
                                if (!pzres)
                                {
                                    logger.Error("反面拍照失败");
                                    LogSafeInvokeAppend("3", "反面拍照失败");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }

                                try
                                {
                                    TaskHelper.WaitUntil(() => cameraDriver.FinishPZ, 25, 3000).Wait();
                                }
                                catch (Exception ex)
                                {
                                    logger.Error("反面拍照返回失败");
                                    LogSafeInvokeAppend("3", "反面拍照返回失败");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }

                                //Task.Delay(TimeSpan.FromSeconds(1.5)).Wait();
                                //if (!cameraDriver.FinishPZ)
                                //{
                                //    logger.Error("正面拍照返回失败");
                                //    LogSafeInvokeAppend("3", "正面拍照返回失败");
                                //    //下发plc结论
                                //    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                //    //下发plc试验完成
                                //    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                //    return;
                                //}
                                //Task.Delay(TimeSpan.FromSeconds(0.5)).Wait();
                                //var checkre = cameraDriver.CheckResult(cameraDriver.ResultPZ, out string type);
                                //if (type == null)
                                //{
                                //    logger.Error("反面拍照返回数据异常");
                                //    return;
                                //}
                                logger.Info($"反面拍照数据{cameraDriver.ResultPZ}");
                                //查询数据
                                var checkre = fileHelper.GetMeasuredValues(cameraDriver.ResultPZ, testindex.FileType, testindex.YZJLimit1, testindex.YZJLimit2, out DownFileData value);
                                if (value == null)
                                {
                                    logger.Error("反面拍照数据获取异常");
                                    LogSafeInvokeAppend("3", "反面拍照数据获取异常");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                    return;
                                }
                                //默认合格
                                if (runConSetting.PassWGJC == "true")
                                {
                                    checkre = true;
                                }
                                //查询数据是否含有当前数据
                                var testresult = db.Set<TestResult>().FirstOrDefault(x => x.SampleNo == no.ToString() && x.LayOffMark != "1");
                                var testresultfirst7 = db.Set<TestResult>().Where(x => x.SampleNo != no.ToString()).OrderByDescending(x => x.CreateTime).ToList().Take(7).ToList();
                                if (testresult != null)
                                {
                                    if (testindex.FileType == "1")
                                    {
                                        testresult.CircleDiameter2 = value.CircleDiameter2;
                                        testresult.CircleDiameterRes2 = runConSetting.PassWGJC == "true" ? "合格" : (value.CircleDiameterRes2=="true"?"合格":"不合格");
                                        if (!checkre)
                                        {
                                            testresult.AppearanceResult = "不合格";
                                        }
                                        if (testresult.MBResult == "合格") 
                                        {
                                            testresult.MBResult = runConSetting.PassWGJC == "true" ? "合格" : (value.MBResult == "true" ? "合格" : "不合格");
                                        }
                                        lock (dataSaveObject)
                                        {
                                            db.Set<TestResult>().Update(testresult);
                                            db.SaveChanges();
                                        }
                                    }
                                    else if (testindex.FileType == "2")
                                    {
                                        testresult.CircleDiameter2 = value.CircleDiameter2;
                                        testresult.CircleDiameterRes2 = runConSetting.PassWGJC == "true" ? "合格" : (value.CircleDiameterRes2 == "true" ? "合格" : "不合格");
                                        if (!checkre)
                                        {
                                            testresult.AppearanceResult = "不合格";
                                        }
                                        if (testresult.MBResult == "合格")
                                        {
                                            testresult.MBResult = runConSetting.PassWGJC == "true" ? "合格" : (value.MBResult == "true" ? "合格" : "不合格");
                                        }
                                        lock (dataSaveObject)
                                        {
                                            db.Set<TestResult>().Update(testresult);
                                            db.SaveChanges();
                                        }
                                    }
                                    else if (testindex.FileType == "3")
                                    {
                                        //先判断是否有重复编号
                                        if (runConSetting.CFBHJC == "true")
                                        {
                                            var cf = CheckRepeatData(value.CheckBit, testresultfirst7);
                                            if (cf)
                                            {
                                                logger.Error($"编号{value.CheckBit}存在重复数据");
                                                LogSafeInvokeAppend("3", $"编号{value.CheckBit}存在重复数据");
                                                return;
                                            }
                                        }
                                        testresult.CheckBit = value.CheckBit;
                                        testresult.AppearanceData = value.CheckBit;
                                        testresult.CircleDiameter2 = value.CircleDiameter2;
                                        testresult.CircleDiameterRes2 = runConSetting.PassWGJC == "true" ? "合格" : (value.CircleDiameterRes2 == "true" ? "合格" : "不合格");
                                        if (!checkre)
                                        {
                                            testresult.AppearanceResult = "不合格";
                                        }
                                        if (testresult.MBResult == "合格")
                                        {
                                            testresult.MBResult = runConSetting.PassWGJC == "true" ? "合格" : (value.MBResult == "true" ? "合格" : "不合格");
                                        }
                                        lock (dataSaveObject)
                                        {
                                            db.Set<TestResult>().Update(testresult);
                                            db.SaveChanges();
                                        }
                                    }
                                    else
                                    {
                                        logger.Error("反面拍照csv文件类型异常");
                                        LogSafeInvokeAppend("3", "反面拍照csv文件类型异常");
                                        //下发plc结论
                                        plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                        //下发plc试验完成
                                        plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                        return;
                                    }
                                }
                                else
                                {
                                    if (testindex.FileType == "1")
                                    {
                                        var testResultNew = new TestResult()
                                        {
                                            Id = Guid.NewGuid().ToString("N"),
                                            SampleNo = no.ToString(),
                                            CircleDiameter2 = value.CircleDiameter2,
                                            CircleDiameterRes2 = runConSetting.PassWGJC == "true" ? "合格" : (value.CircleDiameterRes2 == "true" ? "合格" : "不合格"),
                                            AppearanceResult = checkre ? "合格" : "不合格",
                                            MBResult = runConSetting.PassWGJC == "true" ? "合格" : (value.MBResult == "true" ? "合格" : "不合格"),
                                            CreateTime = DateTime.Now,
                                        };
                                        lock (dataSaveObject)
                                        {
                                            db.Set<TestResult>().Add(testResultNew);
                                            db.SaveChanges();
                                        }
                                    }
                                    else if (testindex.FileType == "2")
                                    {
                                        var testResultNew = new TestResult()
                                        {
                                            Id = Guid.NewGuid().ToString("N"),
                                            SampleNo = no.ToString(),
                                            CircleDiameter2 = value.CircleDiameter2,
                                            CircleDiameterRes2 = runConSetting.PassWGJC == "true" ? "合格" : (value.CircleDiameterRes2 == "true" ? "合格" : "不合格"),
                                            AppearanceResult = checkre ? "合格" : "不合格",
                                            MBResult = runConSetting.PassWGJC == "true" ? "合格" : (value.MBResult == "true" ? "合格" : "不合格"),
                                            CreateTime = DateTime.Now,
                                        };
                                        lock (dataSaveObject)
                                        {
                                            db.Set<TestResult>().Add(testResultNew);
                                            db.SaveChanges();
                                        }
                                    }
                                    else if (testindex.FileType == "3")
                                    {
                                        //先判断是否有重复编号
                                        if (runConSetting.CFBHJC == "true")
                                        {
                                            var cf = CheckRepeatData(value.CheckBit, testresultfirst7);
                                            if (cf)
                                            {
                                                logger.Error($"编号{value.CheckBit}存在重复数据");
                                                LogSafeInvokeAppend("3", $"编号{value.CheckBit}存在重复数据");
                                                return;
                                            }
                                        }
                                        var testResultNew = new TestResult()
                                        {
                                            Id = Guid.NewGuid().ToString("N"),
                                            SampleNo = no.ToString(),
                                            CircleDiameter2 = value.CircleDiameter2,
                                            CircleDiameterRes2 = runConSetting.PassWGJC == "true" ? "合格" : (value.CircleDiameterRes2 == "true" ? "合格" : "不合格"),
                                            CheckBit = value.CheckBit,
                                            AppearanceData = value.CheckBit,
                                            AppearanceResult = checkre ? "合格" : "不合格",
                                            MBResult = runConSetting.PassWGJC == "true" ? "合格" : (value.MBResult == "true" ? "合格" : "不合格"),
                                            CreateTime = DateTime.Now,
                                        };
                                        lock (dataSaveObject)
                                        {
                                            db.Set<TestResult>().Add(testResultNew);
                                            db.SaveChanges();
                                        }
                                    }
                                    else
                                    {
                                        logger.Error("反面拍照csv文件类型异常");
                                        LogSafeInvokeAppend("3", "反面拍照csv文件类型异常");
                                        //下发plc结论
                                        plcDriver.WriteShort(PlcPoint.wgjcjl, 2);
                                        //下发plc试验完成
                                        plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                        return;
                                    }
                                }

                                //下发plc结论
                                ushort resultWgjc = checkre ? (ushort)1 : (ushort)2;
                                plcDriver.WriteShort(PlcPoint.wgjcjl, resultWgjc);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.wgjcdw, 0);
                                //移除数据文件
                                //string getfilePath = AppDomain.CurrentDomain.BaseDirectory + "OriginalRecord\\Resultsq";
                                //var ycjl = fileHelper.MoveFile(runConSetting.DownPictureFileAdd, getfilePath);
                                //if (!ycjl)
                                //{
                                //    logger.Error("下拍照移除文件失败");
                                //}
                            }
                        }
                    }
                }


            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
        }

        /// <summary>
        /// 外观检查模板切换
        /// </summary>
        private void ModelChange()
        {
            try
            {
                //获取到位信号
                var state = plcDriver.ReadShort(PlcPoint.UpdateMaterielNo);
                if (state == -1)
                {
                    logger.Error("外观检查模板切换获取到位信号异常");
                    LogSafeInvokeAppend("3", "外观检查模板切换获取到位信号异常");
                    return;
                }
                else if (state > 50)
                {
                    logger.Error("外观检查模板切换与plc连接失败");
                    LogSafeInvokeAppend("3", "外观检查模板切换与plc连接失败");
                    return;
                }
                else if (state == 1)
                {
                    lock (mbqhObject)
                    {
                        logger.Info($"外观检查模板切换获取到位信号{state}");
                        using (var db = new MyDbContext())
                        {
                            //获取样品类别
                            var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                            var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                            if (testindex == null)
                            {
                                logger.Error($"外观检查模板切换获取物料编号{indexno}信息异常");
                                LogSafeInvokeAppend("3", $"外观检查模板切换获取物料编号{indexno}信息异常");
                                return;
                            }

                            var res = cameraDriver.SetModel(testindex.ModelChangeCommand);
                            if (!res)
                            {
                                logger.Error($"外观检查模板切换获取物料编号{indexno}模板失败");
                                LogSafeInvokeAppend("3", $"外观检查模板切换获取物料编号{indexno}模板失败");
                                return;
                            }

                            try
                            {
                                TaskHelper.WaitUntil(() => cameraDriver.FinishQHMB, 25, 8000).Wait();
                                logger.Info($"物料编号{indexno}模板切换成功");
                                //更新
                                var state2 = plcDriver.WriteShort(PlcPoint.UpdateMaterielNo, 0);
                                if (!state2)
                                {
                                    logger.Error($"物料编号{indexno}模板更新状态复位失败");
                                    LogSafeInvokeAppend("3", $"物料编号{indexno}模板更新状态复位失败");
                                    return;
                                }
                            }
                            catch (Exception ex)
                            {
                                logger.Error(ex);
                                logger.Error($"物料编号{indexno}模板失败");
                                LogSafeInvokeAppend("3", $"物料编号{indexno}模板失败");
                                return;
                            }

                            //Task.Delay(TimeSpan.FromSeconds(8)).Wait();
                            //if (!cameraDriver.FinishQHMB)
                            //{
                            //    logger.Error($"物料编号{indexno}模板失败");
                            //    LogSafeInvokeAppend("3", $"物料编号{indexno}模板失败");
                            //    return;
                            //}
                            //else
                            //{
                            //    logger.Info($"物料编号{indexno}模板切换成功");
                            //    //更新
                            //    var state2 = plcDriver.WriteShort(PlcPoint.UpdateMaterielNo, 0);
                            //    if (!state2)
                            //    {
                            //        logger.Error($"物料编号{indexno}模板更新状态复位失败");
                            //        LogSafeInvokeAppend("3", $"物料编号{indexno}模板更新状态复位失败");
                            //        return;
                            //    }
                            //}
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
        }

        /// <summary>
        /// 检测是否有重复数据
        /// </summary>
        /// <param name="value"></param>
        /// <param name="testResults"></param>
        /// <returns></returns>
        private bool CheckRepeatData(string value, List<TestResult> testResults)
        {
            try
            {
                foreach (TestResult test in testResults)
                {
                    if (test.CheckBit == value)
                    {
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return true;
            }
        }

        /// <summary>
        /// 激光刻印
        /// </summary>
        private void LaserEngraving()
        {
            try
            {
                //获取到位信号
                var state = plcDriver.ReadShort(PlcPoint.jgkmdw);
                if (state == -1)
                {
                    logger.Error("激光刻印获取到位信号异常");
                    LogSafeInvokeAppend("3", "激光刻印获取到位信号异常");
                    return;
                }
                else if (state > 50)
                {
                    logger.Error("激光刻印与plc连接失败");
                    LogSafeInvokeAppend("3", "激光刻印与plc连接失败");
                    return;
                }
                else if (state == 1)
                {
                    Task.Delay(TimeSpan.FromSeconds(1)).Wait();
                    logger.Info($"激光刻印获取到位信号{state}");
                    //连接数据库
                    using (var db = new MyDbContext())
                    {
                        //获取当前产品编号
                        var no = plcDriver.ReadInt(PlcPoint.jgkmcpbh);
                        if (no == -1)
                        {
                            logger.Error($"激光刻印获取产品编号异常{no}");
                            LogSafeInvokeAppend("3", $"激光刻印获取产品编号异常{no}");
                            //下发plc结论
                            plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                            return;
                        }

                        //查询数据是否含有当前数据
                        var testresult = db.Set<TestResult>().FirstOrDefault(x => x.SampleNo == no.ToString() && x.LayOffMark != "1");
                        if (testresult != null)
                        {
                            //获取刻印物料编号
                            var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                            var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                            if (testindex == null)
                            {
                                logger.Error($"激光刻印产品编号{no}获取物料编号{indexno}信息异常");
                                LogSafeInvokeAppend("3", $"激光刻印产品编号{no}获取物料编号{indexno}信息异常");
                                //下发plc结论
                                plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                return;
                            }
                            //获取刻印编号
                            if (testindex.NeedMarkPhoto == "1")
                            {
                                //拍照
                                var pzres = markCameraDriver.TakePicture2();
                                if (!pzres)
                                {
                                    logger.Error("标识符拍照失败");
                                    LogSafeInvokeAppend("3", "标识符拍照失败");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                    return;
                                }
                                try
                                {
                                    TaskHelper.WaitUntil(() => markCameraDriver.FinishPZ, 25, 3000).Wait();
                                }
                                catch (Exception ex)
                                {
                                    logger.Error("标识符拍照返回失败");
                                    LogSafeInvokeAppend("3", "标识符拍照返回失败");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                    return;
                                }
                                logger.Info($"标识符拍照数据{markCameraDriver.ResultPZ}");
                                //查询数据
                                var checkre = fileHelper.GetMeasuredValues(markCameraDriver.ResultPZ, out string markvalue);
                                if (markvalue == null)
                                {
                                    logger.Error("数据获取异常");
                                    LogSafeInvokeAppend("3", "数据获取异常");
                                    logger.Info("重新设置拍照预设值");
                                    //拍照预设值
                                    var yszres = markCameraDriver.PresetValue();
                                    if (!yszres)
                                    {
                                        logger.Error("标识符拍照预设值失败");
                                        LogSafeInvokeAppend("3", "标识符拍照预设值失败");
                                        //下发plc结论
                                        plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                        //下发plc试验完成
                                        plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                        return;
                                    }
                                    try
                                    {
                                        TaskHelper.WaitUntil(() => markCameraDriver.FinishYSZ, 25, 5000).Wait();
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.Error("标识符拍照预设值失败");
                                        LogSafeInvokeAppend("3", "标识符拍照预设值失败");
                                        //下发plc结论
                                        plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                        //下发plc试验完成
                                        plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                        return;
                                    }
                                    logger.Info("重新拍照");
                                    //重新拍照
                                    pzres = markCameraDriver.TakePicture2();
                                    if (!pzres)
                                    {
                                        logger.Error("标识符拍照失败");
                                        LogSafeInvokeAppend("3", "标识符拍照失败");
                                        //下发plc结论
                                        plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                        //下发plc试验完成
                                        plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                        return;
                                    }
                                    try
                                    {
                                        TaskHelper.WaitUntil(() => markCameraDriver.FinishPZ, 25, 3000).Wait();
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.Error("标识符拍照返回失败");
                                        LogSafeInvokeAppend("3", "标识符拍照返回失败");
                                        //下发plc结论
                                        plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                        //下发plc试验完成
                                        plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                        return;
                                    }
                                    logger.Info($"标识符拍照数据{markCameraDriver.ResultPZ}");
                                    //查询数据
                                    checkre = fileHelper.GetMeasuredValues(markCameraDriver.ResultPZ, out markvalue);
                                }
                                if (markvalue == null)
                                {
                                    logger.Error("数据获取异常");
                                    LogSafeInvokeAppend("3", "数据获取异常");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                    return;
                                }
                                if (!checkre)
                                {
                                    logger.Error("标识符获取失败");
                                    LogSafeInvokeAppend("3", "标识符获取失败");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                    return;
                                }
                                //保存数据
                                testresult.CheckBit = markvalue;
                                testresult.AppearanceData = markvalue;
                                lock (dataSaveObject)
                                {
                                    db.Set<TestResult>().Update(testresult);
                                    db.SaveChanges();
                                }
                            }
                            //切换模板
                            var resultXFMB = emDriver.InitModel(testindex.ModelName);
                            Task.Delay(TimeSpan.FromSeconds(0.1)).Wait();
                            if (!resultXFMB)
                            {
                                logger.Error($"激光刻印产品编号{no}下发模板信息异常");
                                LogSafeInvokeAppend("3", $"激光刻印产品编号{no}下发模板信息异常");
                                //下发plc结论
                                plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                return;
                            }
                            //刻印标识符
                            string identifier = null;
                            if (runConSetting.IssueStatus == "1")
                            {
                                if (testresult.AppearanceData == null)
                                {
                                    logger.Error($"激光刻印产品编号{no}的刻印信息不能为空");
                                    LogSafeInvokeAppend("3", $"激光刻印产品编号{no}的刻印信息不能为空");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                    return;
                                }
                                //选择刻印编号
                                var kynoRes = GetLaserEngravingNo(testresult.AppearanceData, out int kyno);
                                if (kynoRes)
                                {
                                    //下发刻印信息
                                    identifier = kyno.ToString();
                                }
                                else
                                {
                                    logger.Error($"激光刻印产品编号{no}刻印编号获取异常");
                                    LogSafeInvokeAppend("3", $"激光刻印产品编号{no}刻印编号获取异常");
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                    return;
                                }
                            }
                            else
                            {
                                identifier = runConSetting.EncodingData;
                            }
                            //获取当前刻印时间
                            var timeKY = DateTime.Now.ToString("ddMMyy");

                            if (runConSetting.TestTime != timeKY)
                            {
                                runConSetting.TestTime = timeKY;
                                runConSetting.IndexKY = 0;
                            }

                            //序号
                            runConSetting.IndexKY = runConSetting.IndexKY + 1;
                            //保存
                            runConSetting.Save();
                            var index = runConSetting.IndexKY.ToString("D5");

                            var dataXF = testindex.MaterielNo + timeKY + index + "5" + identifier;

                            //下发刻印信息
                            var resultXFKY = emDriver.SendEngravingData("BSF", dataXF);
                            Task.Delay(TimeSpan.FromSeconds(0.1)).Wait();
                            if (!resultXFKY)
                            {
                                logger.Error($"激光刻印产品编号{no}下发刻印信息异常");
                                LogSafeInvokeAppend("3", $"激光刻印产品编号{no}下发刻印信息异常");
                                //下发plc结论
                                plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                return;
                            }

                            //开始刻印
                            var resultKY = emDriver.StartEngraving();
                            Task.Delay(TimeSpan.FromSeconds(0.1)).Wait();
                            if (resultKY)
                            {
                                logger.Info($"激光刻印产品编号{no}刻印成功");
                                //下发plc结论
                                plcDriver.WriteShort(PlcPoint.jgkmjl, 1);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                return;
                            }
                            else
                            {
                                logger.Error($"激光刻印产品编号{no}刻印失败");
                                LogSafeInvokeAppend("3", $"激光刻印产品编号{no}刻印失败");
                                //下发plc结论
                                plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                return;
                            }

                        }
                        else
                        {
                            logger.Error($"激光刻印未找到产品编号{no}的信息");
                            LogSafeInvokeAppend("3", $"激光刻印未找到产品编号{no}的信息");
                            //下发plc结论
                            plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                            return;
                        }
                    }
                }
                return;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
                return;
            }
        }

        #region 服务端激光刻印

        /// <summary>
        /// 激光刻印
        /// </summary>
        private MarkingParam LaserEngraving2()
        {
            try
            {
                //获取到位信号
                var state = plcDriver.ReadShort(PlcPoint.jgkmdw);
                if (state == -1)
                {
                    logger.Error("激光刻印获取到位信号异常");
                    LogSafeInvokeAppend("3", "激光刻印获取到位信号异常");
                    return null;
                }
                else if (state > 50)
                {
                    logger.Error("激光刻印与plc连接失败");
                    LogSafeInvokeAppend("3", "激光刻印与plc连接失败");
                    return null;
                }
                else if (state == 1)
                {
                    logger.Info($"激光刻印获取到位信号{state}");
                    //连接数据库
                    using (var db = new MyDbContext())
                    {
                        var markingParam = new MarkingParam();
                        //获取当前产品编号
                        var no = plcDriver.ReadInt(PlcPoint.jgkmcpbh);
                        if (no == -1)
                        {
                            logger.Error($"激光刻印获取产品编号异常{no}");
                            LogSafeInvokeAppend("3", $"激光刻印获取产品编号异常{no}");
                            ////下发plc结论
                            //plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                            ////下发plc试验完成
                            //plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                            return null;
                        }

                        //查询数据是否含有当前数据
                        var testresult = db.Set<TestResult>().FirstOrDefault(x => x.SampleNo == no.ToString() && x.LayOffMark != "1");
                        if (testresult != null)
                        {
                            if (testresult.AppearanceData == null)
                            {
                                logger.Error($"激光刻印产品编号{no}的刻印信息不能为空");
                                LogSafeInvokeAppend("3", $"激光刻印产品编号{no}的刻印信息不能为空");
                                ////下发plc结论
                                //plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                ////下发plc试验完成
                                //plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                return null;
                            }
                            ////获取刻印物料编号
                            //var leDatas = new List<string>();
                            //var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                            //var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                            //if (testindex == null)
                            //{
                            //    logger.Error($"激光刻印产品编号{no}获取物料编号{indexno}信息异常");
                            //    LogSafeInvokeAppend("3", $"激光刻印产品编号{no}获取物料编号{indexno}信息异常");
                            //    ////下发plc结论
                            //    //plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                            //    ////下发plc试验完成
                            //    //plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                            //    return null;
                            //}
                            ////获取物料编号
                            //markingParam.MaterielNo = testindex.MaterielNo;

                            if (runConSetting.IssueStatus == "1")
                            {
                                //选择刻印编号
                                var kynoRes = GetLaserEngravingNo(testresult.AppearanceData, out int kyno);
                                if (kynoRes)
                                {
                                    markingParam.Identifier = kyno.ToString();

                                }
                                else
                                {
                                    logger.Error($"激光刻印产品编号{no}刻印编号获取异常");
                                    LogSafeInvokeAppend("3", $"激光刻印产品编号{no}刻印编号获取异常");
                                    ////下发plc结论
                                    //plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                                    ////下发plc试验完成
                                    //plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                                    return null;
                                }
                            }
                            else
                            {
                                markingParam.Identifier = runConSetting.EncodingData;
                            }

                            //获取样品类别
                            //var type = GetLaserEngravingModel(testresult.AppearanceData);
                            //var timeNow = DateTime.Now.ToString("ddMMyy");

                            //下发刻印信息
                            return markingParam;
                        }
                        else
                        {
                            logger.Error($"激光刻印未找到产品编号{no}的信息");
                            LogSafeInvokeAppend("3", $"激光刻印未找到产品编号{no}的信息");
                            //下发plc结论
                            plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                            return null;
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
                return null;
            }
        }

        /// <summary>
        /// 激光刻印信息发送完成
        /// </summary>
        private void LaserEngravingFinish(string type, bool res)
        {
            try
            {
                if (res)
                {
                    //下发plc结论
                    plcDriver.WriteShort(PlcPoint.jgkmjl, 1);
                    //下发plc试验完成
                    plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                    return;
                }
                else
                {
                    //下发plc结论
                    plcDriver.WriteShort(PlcPoint.jgkmjl, 2);
                    //下发plc试验完成
                    plcDriver.WriteShort(PlcPoint.jgkmdw, 0);
                    return;
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
            }
        }

        #endregion

        /// <summary>
        /// 二维码检测
        /// </summary>
        private void TDCodeDetection()
        {
            try
            {
                //获取到位信号
                var state = plcDriver.ReadShort(PlcPoint.ewmdw);
                if (state == -1)
                {
                    logger.Error("二维码检测获取到位信号异常");
                    LogSafeInvokeAppend("3", "二维码检测获取到位信号异常");
                    return;
                }
                else if (state > 50)
                {
                    logger.Error("二维码检测与plc连接失败");
                    LogSafeInvokeAppend("3", "二维码检测与plc连接失败");
                    return;
                }
                else if (state == 1)
                {
                    logger.Info($"二维码检测获取到位信号{state}");
                    //连接数据库
                    using (var db = new MyDbContext())
                    {
                        //获取当前产品编号
                        var no = plcDriver.ReadInt(PlcPoint.ewmcpbh);
                        if (no == -1)
                        {
                            logger.Error($"二维码检测获取产品编号异常{no}");
                            LogSafeInvokeAppend("3", $"二维码检测获取产品编号异常{no}");
                            //下发plc结论
                            plcDriver.WriteShort(PlcPoint.ewmjl, 2);
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.ewmdw, 0);
                            return;
                        }
                        //读取二维码
                        var ewmres = ewmDriver.TakePicture(out string value, out string limit);
                        if (!ewmres)
                        {
                            logger.Error($"二维码检测产品编号{no}读取二维码信息异常");
                            LogSafeInvokeAppend("3", $"二维码检测产品编号{no}读取二维码信息异常");
                            //下发plc结论
                            plcDriver.WriteShort(PlcPoint.ewmjl, 2);
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.ewmdw, 0);
                            return;
                        }
                        if (value == null || limit == null)
                        {
                            logger.Error($"二维码检测产品编号{no}读取二维码信息不能为空");
                            LogSafeInvokeAppend("3", $"二维码检测产品编号{no}读取二维码信息不能为空");
                            //下发plc结论
                            plcDriver.WriteShort(PlcPoint.ewmjl, 2);
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.ewmdw, 0);
                            return;
                        }
                        //打印到桌面
                        Task.Run(() =>
                        {
                            uiTextBox22.Invoke(UITextBoxChangeData, uiTextBox22, no.ToString());
                            uiTextBox21.Invoke(UITextBoxChangeData, uiTextBox21, value.ToString());
                            uiTextBox20.Invoke(UITextBoxChangeData, uiTextBox20, limit.ToString());
                        });
                        //判断结论
                        var resultTotal = true;
                        if (characters.Contains(limit))
                        {
                            int indexlimit = characters.IndexOf(runConSetting.KMJudgeLimit);
                            int index = characters.IndexOf(limit);

                            if (index <= indexlimit)
                            {
                                resultTotal = true;
                            }
                            else
                            {
                                resultTotal = false;
                            }
                        }
                        else
                        {
                            resultTotal = false;
                        }

                        //获取刻印物料编号
                        var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                        var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                        if (testindex == null)
                        {
                            logger.Error($"二维码检测产品编号{no}获取物料编号{indexno}信息异常");
                            LogSafeInvokeAppend("3", $"二维码检测产品编号{no}获取物料编号{indexno}信息异常");
                            //下发plc结论
                            plcDriver.WriteShort(PlcPoint.ewmjl, 2);
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.ewmdw, 0);
                            return;
                        }
                        var length = value.Length;

                        if (runConSetting.PassCFBM == "false")
                        {
                            //判断物料编号
                            var nowMaterielNo = value.Substring(0, length - 13);
                            if (nowMaterielNo != testindex.MaterielNo)
                            {
                                logger.Error($"二维码检测产品编号{no}物料编号{indexno}不一致");
                                LogSafeInvokeAppend("3", $"二维码检测产品编号{no}物料编号{indexno}不一致");
                                resultTotal = false;
                                //下发plc结论
                                plcDriver.WriteShort(PlcPoint.ewmjl, 2);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.ewmdw, 0);
                                return;
                            }


                            //判断日期
                            var timeNow = DateTime.Now.ToString("ddMMyy");
                            var nowTime = value.Substring(length - 13, 6);
                            if (nowTime != timeNow)
                            {
                                logger.Error($"二维码检测产品编号{no}时间不一致");
                                LogSafeInvokeAppend("3", $"二维码检测产品编号{no}时间不一致");
                                resultTotal = false;
                                //下发plc结论
                                plcDriver.WriteShort(PlcPoint.ewmjl, 2);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.ewmdw, 0);
                                return;
                            }
                        }
                        //查询数据是否含有当前数据
                        var testresult = db.Set<TestResult>().FirstOrDefault(x => x.SampleNo == no.ToString() && x.LayOffMark != "1");
                        if (testresult != null)
                        {
                            //判断序号
                            if (runConSetting.IssueStatus == "1")
                            {
                                var nowNo = value.Substring(length - 1, 1).ToString();
                                var kynoRes = GetLaserEngravingNo(testresult.AppearanceData, out int kyno);
                                if (kyno.ToString() != nowNo)
                                {
                                    logger.Error($"二维码检测产品编号{no}刻印序号不一致");
                                    LogSafeInvokeAppend("3", $"二维码检测产品编号{no}刻印序号不一致");
                                    resultTotal = false;
                                    //下发plc结论
                                    plcDriver.WriteShort(PlcPoint.ewmjl, 2);
                                    //下发plc试验完成
                                    plcDriver.WriteShort(PlcPoint.ewmdw, 0);
                                    return;
                                }
                            }
                            //查询是否有重复编码
                            var testresultcfs = db.Set<TestResult>().Where(x => x.SampleNo != no.ToString() && x.TDCodeData == value).ToList();
                            if (testresultcfs.Any() && runConSetting.PassCFBM == "true")
                            {
                                logger.Error($"二维码检测产品编号{no}存在重复编码{value}");
                                LogSafeInvokeAppend("3", $"二维码检测产品编号{no}存在重复编码{value}");
                                //写入不合格
                                testresult.TDCodeData = value;
                                testresult.TDCodeLimit = limit;
                                testresult.TDCodeResult = "不合格";
                                lock (dataSaveObject)
                                {
                                    db.Set<TestResult>().Update(testresult);
                                    db.SaveChanges();
                                }
                                //下发plc结论
                                plcDriver.WriteShort(PlcPoint.ewmjl, 2);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.ewmdw, 0);
                            }
                            else
                            {
                                logger.Info($"二维码检测产品编号{no}不存在重复编码{value}");
                                //写入结论
                                testresult.TDCodeData = value;
                                testresult.TDCodeLimit = limit;
                                testresult.TDCodeResult = resultTotal ? "合格" : "不合格";
                                lock (dataSaveObject)
                                {
                                    db.Set<TestResult>().Update(testresult);
                                    db.SaveChanges();
                                }
                                var resultData = resultTotal ? (ushort)1 : (ushort)2;
                                //下发plc结论
                                plcDriver.WriteShort(PlcPoint.ewmjl, resultData);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.ewmdw, 0);
                            }
                        }
                        else
                        {
                            logger.Error($"二维码检测未找到产品编号{no}的信息");
                            LogSafeInvokeAppend("3", $"二维码检测未找到产品编号{no}的信息");
                            //判断序号
                            //if (runConSetting.IssueStatus == "1")
                            //{
                            //    var nowNo = value.Substring(length - 1, 1).ToString();
                            //    var kynoRes = GetLaserEngravingNo(testresult.AppearanceData, out int kyno);
                            //    if (kyno.ToString() != nowNo)
                            //    {
                            //        logger.Error($"二维码检测产品编号{no}刻印序号不一致");
                            //        LogSafeInvokeAppend("3", $"二维码检测产品编号{no}刻印序号不一致");
                            //        resultTotal = false;
                            //        //下发plc结论
                            //        plcDriver.WriteShort(PlcPoint.ewmjl, 2);
                            //        //下发plc试验完成
                            //        plcDriver.WriteShort(PlcPoint.ewmdw, 0);
                            //        return;
                            //    }
                            //}
                            //查询是否有重复编码
                            var testresultcfs = db.Set<TestResult>().Where(x => x.SampleNo != no.ToString() && x.TDCodeData == value).ToList();
                            if (testresultcfs.Any() && runConSetting.PassCFBM == "true")
                            {
                                logger.Error($"二维码检测产品编号{no}存在重复编码{value}");
                                LogSafeInvokeAppend("3", $"二维码检测产品编号{no}存在重复编码{value}");
                                //重新录入
                                var testResultNew = new TestResult()
                                {
                                    Id = Guid.NewGuid().ToString("N"),
                                    SampleNo = no.ToString(),
                                    TDCodeData = value,
                                    TDCodeLimit = limit,
                                    TDCodeResult = "不合格",
                                    CreateTime = DateTime.Now,
                                };
                                lock (dataSaveObject)
                                {
                                    db.Set<TestResult>().Add(testResultNew);
                                    db.SaveChanges();
                                }
                                //下发plc结论
                                plcDriver.WriteShort(PlcPoint.ewmjl, 2);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.ewmdw, 0);
                            }
                            else
                            {
                                logger.Info($"二维码检测产品编号{no}不存在重复编码{value}");
                                //重新录入
                                var testResultNew = new TestResult()
                                {
                                    Id = Guid.NewGuid().ToString("N"),
                                    SampleNo = no.ToString(),
                                    TDCodeData = value,
                                    TDCodeLimit = limit,
                                    TDCodeResult = resultTotal ? "合格" : "不合格",
                                    CreateTime = DateTime.Now,
                                };
                                lock (dataSaveObject)
                                {
                                    db.Set<TestResult>().Add(testResultNew);
                                    db.SaveChanges();
                                }
                                var resultData = resultTotal ? (ushort)1 : (ushort)2;
                                //下发plc结论
                                plcDriver.WriteShort(PlcPoint.ewmjl, resultData);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.ewmdw, 0);
                            }

                            
                            //下发plc结论
                            plcDriver.WriteShort(PlcPoint.ewmjl, 2);
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.ewmdw, 0);
                            return;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
        }

        /// <summary>
        /// 视觉前高度检测
        /// </summary>
        private void HeightDetectionSJQ()
        {
            try
            {
                //获取到位信号
                var state = plcDriver.ReadShort(PlcPoint.sjqgdjcdw);
                if (state == -1)
                {
                    logger.Error("视觉前高度检测获取到位信号异常");
                    LogSafeInvokeAppend("3", "视觉前高度检测获取到位信号异常");
                    return;
                }
                else if (state > 50)
                {
                    logger.Error("视觉前高度检测与plc连接失败");
                    LogSafeInvokeAppend("3", "视觉前高度检测与plc连接失败");
                    return;
                }
                else if (state == 1)
                {
                    Task.Delay(TimeSpan.FromSeconds(0.3)).Wait();
                    logger.Info($"视觉前高度检测获取到位信号{state}");
                    //连接数据库
                    using (var db = new MyDbContext())
                    {
                        //获取样品类别
                        var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                        var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                        if (testindex == null)
                        {
                            logger.Error($"外观检查模板切换获取物料编号{indexno}信息异常");
                            LogSafeInvokeAppend("3", $"外观检查模板切换获取物料编号{indexno}信息异常");
                            plcDriver.WriteShort(PlcPoint.sjqgdjcdw, 0);
                            return;
                        }
                        //获取当前产品编号
                        var no = plcDriver.ReadInt(PlcPoint.sjqgdjccpbh);
                        if (no == -1)
                        {
                            logger.Error($"视觉前高度检测获取产品编号异常{no}");
                            LogSafeInvokeAppend("3", $"视觉前高度检测获取产品编号异常{no}");
                            plcDriver.WriteShort(PlcPoint.sjqgdjcdw, 0);
                            return;
                        }
                        //读取高度
                        var readres = heightCheckDriverSJQ.ReadHeightData(out List<string> heightDatas);
                        if (!readres)
                        {
                            logger.Error($"视觉前高度检测产品编号{no}读取高度信息异常");
                            LogSafeInvokeAppend("3", $"视觉前高度检测产品编号{no}读取高度信息异常");
                            plcDriver.WriteShort(PlcPoint.sjqgdjcdw, 0);
                            return;
                        }
                        Task.Run(() =>
                        {
                            //数据展示
                            var resultgd = ChangeHeightData(no.ToString(), ref heightDatas, state.ToString(), testindex.HeightType, testindex.HeightCompensate);

                            //结果数据
                            var data = string.Join(",", heightDatas);

                            using (var dc = new MyDbContext())
                            {
                                //查询数据是否含有当前数据
                                var testresult = dc.Set<TestResult>().FirstOrDefault(x => x.SampleNo == no.ToString() && x.LayOffMark != "1");
                                if (testresult != null)
                                {
                                    //写入结论
                                    testresult.HeightData1 = data;
                                    testresult.HeightResult1 = resultgd ? "合格" : "不合格";
                                    lock (dataSaveObject)
                                    {
                                        dc.Set<TestResult>().Update(testresult);
                                        dc.SaveChanges();
                                    }
                                    //下发plc结论
                                    //plcDriver.WriteShort(PlcPoint.ewmjl, resultData);

                                }
                                else
                                {
                                    var testresultnew = new TestResult()
                                    {
                                        Id = Guid.NewGuid().ToString("N"),
                                        SampleNo = no.ToString(),
                                        HeightData1 = data,
                                        HeightResult1 = resultgd ? "合格" : "不合格",
                                        CreateTime = DateTime.Now,
                                    };
                                    lock (dataSaveObject)
                                    {
                                        dc.Set<TestResult>().Add(testresultnew);
                                        dc.SaveChanges();
                                    }
                                    logger.Info($"视觉前高度检测创建产品编号{no}的信息");
                                    //LogSafeInvokeAppend("3", $"高度检测未找到产品编号{no}的信息");
                                }
                            }
                        });
                        //下发plc试验完成
                        plcDriver.WriteShort(PlcPoint.sjqgdjcdw, 0);
                    }
                }
                else if (state == 2)
                {
                    Task.Delay(TimeSpan.FromSeconds(0.3)).Wait();
                    logger.Info($"视觉前高度检测获取到位信号{state}");
                    //连接数据库
                    using (var db = new MyDbContext())
                    {
                        //获取样品类别
                        var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                        var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                        if (testindex == null)
                        {
                            logger.Error($"外观检查模板切换获取物料编号{indexno}信息异常");
                            LogSafeInvokeAppend("3", $"外观检查模板切换获取物料编号{indexno}信息异常");
                            plcDriver.WriteShort(PlcPoint.sjqgdjcdw, 0);
                            return;
                        }
                        //获取当前产品编号
                        var no = plcDriver.ReadInt(PlcPoint.sjqgdjccpbh);
                        if (no == -1)
                        {
                            logger.Error($"视觉前高度检测获取产品编号异常{no}");
                            LogSafeInvokeAppend("3", $"视觉前高度检测获取产品编号异常{no}");
                            plcDriver.WriteShort(PlcPoint.sjqgdjcdw, 0);
                            return;
                        }
                        //读取高度
                        var readres = heightCheckDriverSJQ.ReadHeightData(out List<string> heightDatas);
                        if (!readres)
                        {
                            logger.Error($"视觉前高度检测产品编号{no}读取高度信息异常");
                            LogSafeInvokeAppend("3", $"视觉前高度检测产品编号{no}读取高度信息异常");
                            plcDriver.WriteShort(PlcPoint.sjqgdjcdw, 0);
                            return;
                        }
                        Task.Run(() =>
                        {
                            //数据展示
                            var resultgd = ChangeHeightData(no.ToString(), ref heightDatas, state.ToString(), testindex.HeightType, testindex.HeightCompensate);

                            //结果数据
                            var data = string.Join(",", heightDatas);

                            using (var dc = new MyDbContext())
                            {
                                //查询数据是否含有当前数据
                                var testresult = dc.Set<TestResult>().FirstOrDefault(x => x.SampleNo == no.ToString() && x.LayOffMark != "1");
                                if (testresult != null)
                                {
                                    //写入结论
                                    testresult.HeightData2 = data;
                                    testresult.HeightResult2 = resultgd ? "合格" : "不合格";
                                    lock (dataSaveObject)
                                    {
                                        dc.Set<TestResult>().Update(testresult);
                                        dc.SaveChanges();
                                    }
                                    //下发plc结论
                                    //plcDriver.WriteShort(PlcPoint.ewmjl, resultData);

                                }
                                else
                                {
                                    var testresultnew = new TestResult()
                                    {
                                        Id = Guid.NewGuid().ToString("N"),
                                        SampleNo = no.ToString(),
                                        HeightData2 = data,
                                        CreateTime = DateTime.Now,
                                    };
                                    lock (dataSaveObject)
                                    {
                                        dc.Set<TestResult>().Add(testresultnew);
                                        dc.SaveChanges();
                                    }
                                    logger.Info($"视觉前高度检测创建产品编号{no}的信息");
                                    //LogSafeInvokeAppend("3", $"高度检测未找到产品编号{no}的信息");
                                }
                            }
                        });
                        //下发plc试验完成
                        plcDriver.WriteShort(PlcPoint.sjqgdjcdw, 0);
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
        }

        /// <summary>
        /// 高度检测
        /// </summary>
        private void HeightDetection()
        {
            try
            {
                //获取到位信号
                var state = plcDriver.ReadShort(PlcPoint.gdjcdw);
                if (state == -1)
                {
                    logger.Error("高度检测获取到位信号异常");
                    LogSafeInvokeAppend("3", "高度检测获取到位信号异常");
                    return;
                }
                else if (state > 50)
                {
                    logger.Error("高度检测与plc连接失败");
                    LogSafeInvokeAppend("3", "高度检测与plc连接失败");
                    return;
                }
                else if (state == 1)
                {
                    Task.Delay(TimeSpan.FromSeconds(0.3)).Wait();
                    logger.Info($"高度检测获取到位信号{state}");
                    //连接数据库
                    using (var db = new MyDbContext())
                    {
                        //获取当前产品编号
                        var no = plcDriver.ReadInt(PlcPoint.gdjccpbh);
                        if (no == -1)
                        {
                            logger.Error($"高度检测获取产品编号异常{no}");
                            LogSafeInvokeAppend("3", $"高度检测获取产品编号异常{no}");
                            plcDriver.WriteShort(PlcPoint.gdjcdw, 0);
                            return;
                        }
                        //获取样品类别
                        var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                        //var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                        //if (testindex == null)
                        //{
                        //    logger.Error($"高度检测获取物料编号{indexno}信息异常");
                        //    LogSafeInvokeAppend("3", $"高度检测获取物料编号{indexno}信息异常");
                        //    plcDriver.WriteShort(PlcPoint.gdjcdw, 0);
                        //    return;
                        //}
                        //读取高度
                        var readres = heightCheckDriver.ReadHeightData(out List<string> heightDatas);
                        if (!readres)
                        {
                            logger.Error($"高度检测产品编号{no}读取高度信息异常");
                            LogSafeInvokeAppend("3", $"高度检测产品编号{no}读取高度信息异常");
                            plcDriver.WriteShort(PlcPoint.gdjcdw, 0);
                            return;
                        }
                        Task.Run(() =>
                        {
                            using (var dc = new MyDbContext())
                            {

                                //var testindex2 = dc.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                                //数据展示
                                ChangeHeightData2(no.ToString(), ref heightDatas, indexno.ToString());

                                //结果数据
                                var data = string.Join(",", heightDatas);
                                //查询数据是否含有当前数据
                                var testresult = dc.Set<TestResult>().FirstOrDefault(x => x.SampleNo == no.ToString() && x.LayOffMark != "1");
                                if (testresult != null)
                                {
                                    //写入结论
                                    testresult.HeightData3 = data;
                                    lock (dataSaveObject)
                                    {
                                        dc.Set<TestResult>().Update(testresult);
                                        dc.SaveChanges();
                                    }
                                    //下发plc结论
                                    //plcDriver.WriteShort(PlcPoint.ewmjl, resultData);

                                }
                                else
                                {
                                    logger.Error($"高度检测未找到产品编号{no}的信息");
                                    LogSafeInvokeAppend("3", $"高度检测未找到产品编号{no}的信息");
                                    //重新录入
                                    var testResultNew = new TestResult()
                                    {
                                        Id = Guid.NewGuid().ToString("N"),
                                        SampleNo = no.ToString(),
                                        HeightData3 = data,
                                        CreateTime = DateTime.Now,
                                    };
                                    lock (dataSaveObject)
                                    {
                                        db.Set<TestResult>().Add(testResultNew);
                                        db.SaveChanges();
                                    }
                                }
                            }
                        });
                        //下发plc试验完成
                        plcDriver.WriteShort(PlcPoint.gdjcdw, 0);
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
        }

        /// <summary>
        /// 同轴度检测
        /// </summary>
        private void CoaxialityDetection()
        {
            try
            {
                //获取到位信号
                var state = plcDriver.ReadShort(PlcPoint.tzdjcdw);
                if (state == -1)
                {
                    logger.Error("同轴度检测获取到位信号异常");
                    LogSafeInvokeAppend("3", "同轴度检测获取到位信号异常");
                    return;
                }
                else if (state == 0)
                {
                    return;
                }
                else if (state > 50)
                {
                    logger.Error("同轴度检测与plc连接失败");
                    LogSafeInvokeAppend("3", "同轴度检测与plc连接失败");
                    return;
                }
                else
                {
                    logger.Info($"同轴度检测获取到位信号{state}");
                    //连接数据库
                    using (var db = new MyDbContext())
                    {
                        //获取当前产品编号
                        var no = plcDriver.ReadInt(PlcPoint.tzdjccpbh);
                        if (no == -1)
                        {
                            logger.Error($"同轴度检测获取产品编号异常{no}");
                            LogSafeInvokeAppend("3", $"同轴度检测获取产品编号异常{no}");
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.tzdjcdw, 0);
                            return;
                        }
                        //结果数据
                        var res = state == 1 ? "合格" : "不合格";

                        //查询数据是否含有当前数据
                        var testresult = db.Set<TestResult>().FirstOrDefault(x => x.SampleNo == no.ToString() && x.LayOffMark != "1");
                        if (testresult != null)
                        {
                            //写入结论
                            testresult.ConcentricityResult = res;
                            lock (dataSaveObject)
                            {
                                db.Set<TestResult>().Update(testresult);
                                db.SaveChanges();
                            }
                            //下发plc结论
                            //plcDriver.WriteShort(PlcPoint.ewmjl, resultData);
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.tzdjcdw, 0);
                        }
                        else
                        {
                            logger.Error($"同轴度检测未找到产品编号{no}的信息");
                            LogSafeInvokeAppend("3", $"同轴度检测未找到产品编号{no}的信息");
                            //重新录入
                            var testResultNew = new TestResult()
                            {
                                Id = Guid.NewGuid().ToString("N"),
                                SampleNo = no.ToString(),
                                ConcentricityResult = res,
                                CreateTime = DateTime.Now,
                            };
                            lock (dataSaveObject)
                            {
                                db.Set<TestResult>().Add(testResultNew);
                                db.SaveChanges();
                            }
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.tzdjcdw, 0);
                            return;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
        }

        /// <summary>
        /// 下料
        /// </summary>
        private void FinishedBlanking()
        {
            try
            {
                //获取到位信号
                var state = plcDriver.ReadShort(PlcPoint.xldw);
                if (state == -1)
                {
                    logger.Error("下料获取到位信号异常");
                    LogSafeInvokeAppend("3", "下料获取到位信号异常");
                    return;
                }
                else if (state > 50)
                {
                    logger.Error("下料与plc连接失败");
                    LogSafeInvokeAppend("3", "下料与plc连接失败");
                    return;
                }
                else if (state == 1)
                {
                    logger.Info($"下料获取到位信号{state}");
                    //连接数据库
                    using (var db = new MyDbContext())
                    {
                        //获取当前产品编号
                        var no = plcDriver.ReadInt(PlcPoint.xlcpbh);
                        if (no == -1)
                        {
                            logger.Error($"下料获取产品编号异常{no}");
                            LogSafeInvokeAppend("3", $"下料获取产品编号异常{no}");
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.xldw, 0);
                            return;
                        }

                        //查询数据是否含有当前数据
                        var testresult = db.Set<TestResult>().FirstOrDefault(x => x.SampleNo == no.ToString() && x.LayOffMark != "1");
                        if (testresult != null)
                        {
                            var conclusion = "合格";
                            if ((runConSetting.GDJC == "true" && testresult.HeightResult1 != "合格") || (runConSetting.GDJC == "true" && testresult.HeightResult2 != "合格") ||
                                (runConSetting.WGJC == "true" && testresult.AppearanceResult != "合格") || (runConSetting.NYJC == "true" && testresult.IrResult != "合格") ||
                                 (runConSetting.EWMJC == "true" && testresult.TDCodeResult != "合格") || (runConSetting.TZDJC == "true" && testresult.ConcentricityResult != "合格"))
                            {
                                conclusion = "不合格";
                            }
                            testresult.Conclusion = conclusion;
                            lock (dataSaveObject)
                            {
                                db.Set<TestResult>().Update(testresult);
                                db.SaveChanges();
                            }
                            //组合结论
                            var saveData = new SaveFileData()
                            {
                                SampleNo = testresult.SampleNo,
                                CheckBit = testresult.CheckBit,
                                CircleDiameter1 = testresult.CircleDiameter1,
                                CircleDiameter2 = testresult.CircleDiameter2,
                                AppearanceData = testresult.AppearanceData,
                                IrData = testresult.IrData,
                                IrResult = testresult.IrResult,
                                TDCodeData = testresult.TDCodeData,
                                TDCodeLimit = testresult.TDCodeLimit,
                                HeightData1 = testresult.HeightData1,
                                HeightData2 = testresult.HeightData2,
                                HeightData3 = testresult.HeightData3,
                                ConcentricityData = testresult.ConcentricityData,
                                CreateTime = testresult.CreateTime.ToString()
                            };
                            //保存数据
                            var saveDateTime = DateTime.Now.ToString("yyyyMMdd");
                            var saveFileName = saveDateTime + ".csv";
                            var saveResult = fileHelper.SaveMeasuredValues(runConSetting.SaveFileAdd, saveFileName, saveData);
                            if (saveResult)
                            {
                                if (no != 0)
                                {
                                    testresult.LayOffMark = "1";
                                    lock (dataSaveObject)
                                    {
                                        db.Set<TestResult>().Update(testresult);
                                        db.SaveChanges();
                                    }
                                }
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.xldw, 0);
                                Task.Run(() =>
                                {
                                    DataPresentation();
                                });
                            }
                            else
                            {
                                logger.Error($"下料产品编号{no}保存数据失败");
                                LogSafeInvokeAppend("3", $"下料产品编号{no}保存数据失败");
                                return;
                            }
                        }
                        else
                        {
                            logger.Error($"下料未找到产品编号{no}的信息");
                            LogSafeInvokeAppend("3", $"下料未找到产品编号{no}的信息");
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.xldw, 0);
                            return;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
        }

        /// <summary>
        /// 绝缘电阻试验
        /// </summary>
        private void InsulationResistance()
        {
            try
            {
                //获取到位信号
                var state = plcDriver.ReadShort(PlcPoint.nydw);
                if (state == -1)
                {
                    logger.Error("耐压到位信号异常");
                    LogSafeInvokeAppend("3", "耐压到位信号异常");
                    return;
                }
                else if (state > 50)
                {
                    logger.Error("耐压与plc连接失败");
                    LogSafeInvokeAppend("3", "耐压与plc连接失败");
                    return;
                }
                else if (state == 1)
                {
                    logger.Info($"耐压获取到位信号{state}");
                    //连接数据库
                    using (var db = new MyDbContext())
                    {
                        //获取样品类别
                        var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                        var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                        if (testindex == null)
                        {
                            logger.Error($"耐压获取物料编号{indexno}信息异常");
                            LogSafeInvokeAppend("3", $"耐压获取物料编号{indexno}信息异常");
                            //下发结论
                            plcDriver.WriteShort(PlcPoint.nyjl, 2);
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.nydw, 0);
                            return;
                        }
                        //获取当前产品编号
                        var no = plcDriver.ReadInt(PlcPoint.nycpbh);
                        logger.Info($"耐压获取物料编号{indexno}");
                        if (no == -1)
                        {
                            logger.Error($"耐压获取产品编号异常{no}");
                            LogSafeInvokeAppend("3", $"耐压获取产品编号异常{no}");
                            //下发结论
                            plcDriver.WriteShort(PlcPoint.nyjl, 2);
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.nydw, 0);
                            return;
                        }
                        //获取参数
                        var nyset = nySetDatas.Where(x => x.SampleName == testindex.SampleName).FirstOrDefault();
                        if (nyset == null)
                        {
                            logger.Error($"耐压获取物料编号{indexno}参数设置信息异常");
                            LogSafeInvokeAppend("3", $"耐压获取物料编号{indexno}参数设置信息异常");
                            //下发结论
                            plcDriver.WriteShort(PlcPoint.nyjl, 2);
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.nydw, 0);
                            return;
                        }
                        //判断试验项
                        if (runConSetting.TestItem == 1)
                        {
                            //设置参数
                            //停止
                            nyDriver.SetTest(0);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //1、设置测试组 0x4000
                            nyDriver.SetTestGroup(1);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //2、设置测试模式 0x4001
                            nyDriver.SetTestModel(3);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //3、设置绝缘电阻电压值 0x4030
                            nyDriver.SetIrVoltage((ushort)nyset.NyyVoltage);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //4、设置电阻量程 0x4031
                            nyDriver.SetIrRange((ushort)nyset.IrRange);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //5、设置电阻上限比较使能 0x4032
                            //6、设置电阻上下限值 0x4033道4036
                            //nyDriver.SetIrUpperLimit(runConSetting.IrUpperLimit);
                            nyDriver.SetIrLowerLimit((float)nyset.IrLowerLimit);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //7、设置电阻等待时间 0x4037
                            nyDriver.SetIrWaitingTime(0.5);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //8、设置电阻测试时间 0x4038
                            nyDriver.SetIrTestTime((double)nyset.IrTestTime);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //9、启动测试 0x4004
                            nyDriver.SetTest(1);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //10、判断测试状态为测试完成 0x3001
                            var stopwatch = new Stopwatch();
                            stopwatch.Start();
                            var testStatus = false;
                            while (true)
                            {
                                if (stopwatch.Elapsed > TimeSpan.FromSeconds((double)nyset.IrTestTime + 2))
                                {
                                    logger.Error($"耐压编号{indexno}试验超时");
                                    testStatus = false;
                                    break;
                                }
                                else
                                {
                                    var readstate = nyDriver.ReadState1();
                                    if (readstate == "2")
                                    {
                                        testStatus = true;
                                        break;
                                    }
                                    else
                                    {
                                        Task.Delay(TimeSpan.FromMilliseconds(300)).Wait();
                                        continue;
                                    }
                                }
                            }
                            stopwatch.Stop();

                            string testData = null;
                            string testResult = "2";
                            if (testStatus)
                            {
                                //11、读取测试数据 0x3005和3006
                                testData = nyDriver.ReadData1();
                                logger.Info($"耐压读取数值为{testData}");
                                if (testData.Contains("Infinity") || testData.Contains("infinity") || testData.Contains("∞"))
                                {
                                    testData = "99GΩ";
                                }
                                else
                                {
                                    testData = testData + "Ω";
                                }
                                Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                                //12、读取测试结果 0x3007
                                testResult = nyDriver.ReadResult1();
                                Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                                nyDriver.SetTest(0);
                            }
                            else
                            {
                                nyDriver.SetTest(0);
                                //停止试验
                            }
                            ushort nyresult = testResult == "1" ? (ushort)1 : (ushort)2;

                            //查询数据是否含有当前数据
                            var testresult = db.Set<TestResult>().FirstOrDefault(x => x.SampleNo == no.ToString() && x.LayOffMark != "1");
                            if (testresult != null)
                            {
                                //保存数据
                                testresult.IrData = testData;
                                testresult.IrResult = testResult == "1" ? "合格" : "不合格";
                                lock (dataSaveObject)
                                {
                                    db.Set<TestResult>().Update(testresult);
                                    db.SaveChanges();
                                }
                                //下发结论
                                plcDriver.WriteShort(PlcPoint.nyjl, nyresult);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.nydw, 0);
                                Task.Run(() =>
                                {
                                    //数据展示
                                    uiTextBox8.Invoke(UITextBoxChangeData, uiTextBox8, no.ToString());
                                    uiTextBox7.Invoke(UITextBoxChangeData, uiTextBox7, testData);
                                    var disResult = testResult == "1" ? "合格" : "不合格";
                                    uiTextBox6.Invoke(UITextBoxChangeData, uiTextBox6, disResult);
                                });
                            }
                            else
                            {
                                logger.Error($"耐压未找到产品编号{no}的信息");
                                LogSafeInvokeAppend("3", $"耐压未找到产品编号{no}的信息");
                                var testResultNew = new TestResult()
                                {
                                    Id = Guid.NewGuid().ToString("N"),
                                    SampleNo = no.ToString(),
                                    IrData = testData,
                                    IrResult = testResult == "1" ? "合格" : "不合格",
                                    CreateTime = DateTime.Now,
                                };
                                lock (dataSaveObject)
                                {
                                    db.Set<TestResult>().Add(testResultNew);
                                    db.SaveChanges();
                                }

                                //下发结论
                                plcDriver.WriteShort(PlcPoint.nyjl, nyresult);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.nydw, 0);

                                Task.Run(() =>
                                {
                                    //数据展示
                                    uiTextBox8.Invoke(UITextBoxChangeData, uiTextBox8, no.ToString());
                                    uiTextBox7.Invoke(UITextBoxChangeData, uiTextBox7, testData);
                                    var disResult = testResult == "1" ? "合格" : "不合格";
                                    uiTextBox6.Invoke(UITextBoxChangeData, uiTextBox6, disResult);
                                });
                            }
                        }
                        else if (runConSetting.TestItem == 2)
                        {
                            //设置参数
                            //停止
                            nyDriver.SetTest(0);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //1、设置测试组 0x4000
                            nyDriver.SetTestGroup(1);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //2、设置测试模式 0x4001
                            nyDriver.SetTestModel(2);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //3、设置直流电流电压值
                            nyDriver.SetDcVoltage((ushort)nyset.DcVoltage);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //4、设置上下限值
                            nyDriver.SetDcUpperLimit((double)nyset.DcUpperLimit);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            nyDriver.SetDcLowerLimit((double)nyset.DcLowerLimit);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //5、设置电流上升时间
                            nyDriver.SetDcRiseTime((double)nyset.DcRiseTime);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //6、设置等待时间
                            nyDriver.SetDcTestTime((double)nyset.DcTestTime);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //7、设置电流灵敏度
                            nyDriver.SetDcSensitivity((ushort)nyset.DcSensitivity);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();

                            //8、启动测试 0x4004
                            nyDriver.SetTest(1);
                            Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                            //10、判断测试状态为测试完成 0x3001
                            var stopwatch = new Stopwatch();
                            stopwatch.Start();
                            var testStatus = false;
                            while (true)
                            {
                                if (stopwatch.Elapsed > TimeSpan.FromSeconds((double)nyset.DcTestTime + 2))
                                {
                                    logger.Error($"耐压编号{indexno}试验超时");
                                    testStatus = false;
                                    break;
                                }
                                else
                                {
                                    var readstate = nyDriver.ReadState1();
                                    if (readstate == "2")
                                    {
                                        testStatus = true;
                                        break;
                                    }
                                    else
                                    {
                                        Task.Delay(TimeSpan.FromMilliseconds(300)).Wait();
                                        continue;
                                    }
                                }
                            }
                            stopwatch.Stop();

                            string testData = null;
                            string testResult = "2";
                            if (testStatus)
                            {
                                //11、读取测试数据 0x3005和3006
                                testData = nyDriver.ReadData1();
                                logger.Info($"耐压读取数值为{testData}");
                                //12、读取测试结果 0x3007
                                testResult = nyDriver.ReadResult1();
                                Task.Delay(TimeSpan.FromMilliseconds(20)).Wait();
                                nyDriver.SetTest(0);
                            }
                            else
                            {
                                nyDriver.SetTest(0);
                                //停止试验
                            }
                            ushort nyresult = testResult == "1" ? (ushort)1 : (ushort)2;
                            //查询数据是否含有当前数据
                            var testresult = db.Set<TestResult>().FirstOrDefault(x => x.SampleNo == no.ToString() && x.LayOffMark != "1");
                            if (testresult != null)
                            {
                                //保存数据
                                testresult.DcData = testData;
                                testresult.DcResult = testResult == "1" ? "合格" : "不合格";
                                lock (dataSaveObject)
                                {
                                    db.Set<TestResult>().Update(testresult);
                                    db.SaveChanges();
                                }
                                //下发结论
                                plcDriver.WriteShort(PlcPoint.nyjl, nyresult);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.nydw, 0);
                                Task.Run(() =>
                                {
                                    //数据展示
                                    uiTextBox8.Invoke(UITextBoxChangeData, uiTextBox8, no.ToString());
                                    uiTextBox7.Invoke(UITextBoxChangeData, uiTextBox7, testData);
                                    var disResult = testResult == "1" ? "合格" : "不合格";
                                    uiTextBox6.Invoke(UITextBoxChangeData, uiTextBox6, disResult);
                                });
                            }
                            else
                            {
                                logger.Error($"耐压未找到产品编号{no}的信息");
                                LogSafeInvokeAppend("3", $"耐压未找到产品编号{no}的信息");
                                var testResultNew = new TestResult()
                                {
                                    Id = Guid.NewGuid().ToString("N"),
                                    SampleNo = no.ToString(),
                                    DcData = testData,
                                    DcResult = testResult == "1" ? "合格" : "不合格",
                                    CreateTime = DateTime.Now,
                                };
                                lock (dataSaveObject)
                                {
                                    db.Set<TestResult>().Add(testResultNew);
                                    db.SaveChanges();
                                }
                                //下发结论
                                plcDriver.WriteShort(PlcPoint.nyjl, nyresult);
                                //下发plc试验完成
                                plcDriver.WriteShort(PlcPoint.nydw, 0);

                                Task.Run(() =>
                                {
                                    //数据展示
                                    uiTextBox8.Invoke(UITextBoxChangeData, uiTextBox8, no.ToString());
                                    uiTextBox7.Invoke(UITextBoxChangeData, uiTextBox7, testData);
                                    var disResult = testResult == "1" ? "合格" : "不合格";
                                    uiTextBox6.Invoke(UITextBoxChangeData, uiTextBox6, disResult);
                                });
                            }
                        }
                        else
                        {
                            logger.Error($"耐压获取试验项信息异常");
                            LogSafeInvokeAppend("3", $"耐压获取获取试验项信息异常");
                            //下发结论
                            plcDriver.WriteShort(PlcPoint.nyjl, 2);
                            //下发plc试验完成
                            plcDriver.WriteShort(PlcPoint.nydw, 0);
                            return;
                        }
                    }
                }
            }
            catch (NullReferenceException e) 
            {
                logger.Error(e);
                LogSafeInvokeAppend("3", e.Message);
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
        }

        /// <summary>
        /// 高度复位
        /// </summary>
        private void ReHeightStandard() 
        {
            try
            {
                //获取到位信号
                var state = plcDriver.ReadShort(PlcPoint.gdfwdz1);
                if (state == -1)
                {
                    logger.Error("G0,250高度复位获取到位信号异常");
                    LogSafeInvokeAppend("3", "G0,250高度复位获取到位信号异常");
                    return;
                }
                else if (state > 50)
                {
                    logger.Error("G0,250高度复位与plc连接失败");
                    LogSafeInvokeAppend("3", "G0,250高度复位与plc连接失败");
                    return;
                }
                else if (state == 1)
                {
                    logger.Info($"G0,250高度复位获取到位信号{state}");
                    var result1 = heightCheckDriverSJQ.ReHeighStandard("04");
                    if (!result1) 
                    {
                        logger.Error("G0,250探针1高度复位失败");
                        LogSafeInvokeAppend("3", "G0,250探针1高度复位失败");
                    }
                    Task.Delay(TimeSpan.FromSeconds(1)).Wait();
                    var result2 = heightCheckDriverSJQ.ReHeighStandard("05");
                    if (!result2)
                    {
                        logger.Error("G0,250探针2高度复位失败");
                        LogSafeInvokeAppend("3", "G0,250探针2高度复位失败");
                    }
                    Task.Delay(TimeSpan.FromSeconds(1)).Wait();
                    var result3 = heightCheckDriverSJQ.ReHeighStandard("06");
                    if (!result3)
                    {
                        logger.Error("G0,250探针3高度复位失败");
                        LogSafeInvokeAppend("3", "G0,250探针3高度复位失败");
                    }
                    if (result1 && result2 && result3) 
                    {
                        var resultfw = plcDriver.WriteShort(PlcPoint.gdfwdz1, 0);
                        if (!resultfw) 
                        {
                            logger.Error("G0,250高度复位信号复位失败");
                            LogSafeInvokeAppend("3", "G0,250高度复位信号复位失败");
                        }
                    }
                }
                //获取到位信号
                var state2 = plcDriver.ReadShort(PlcPoint.gdfwdz2);
                if (state2 == -1)
                {
                    logger.Error("G0,150高度复位获取到位信号异常");
                    LogSafeInvokeAppend("3", "G0,150高度复位获取到位信号异常");
                    return;
                }
                else if (state2 > 50)
                {
                    logger.Error("G0,150高度复位与plc连接失败");
                    LogSafeInvokeAppend("3", "G0,150高度复位与plc连接失败");
                    return;
                }
                else if (state2 == 1)
                {
                    logger.Info($"G0,150高度复位获取到位信号{state2}");
                    var result1 = heightCheckDriverSJQ.ReHeighStandard("01");
                    if (!result1)
                    {
                        logger.Error("G0,150探针1高度复位失败");
                        LogSafeInvokeAppend("3", "G0,150探针1高度复位失败");
                    }
                    Task.Delay(TimeSpan.FromSeconds(1)).Wait();
                    var result2 = heightCheckDriverSJQ.ReHeighStandard("02");
                    if (!result2)
                    {
                        logger.Error("G0,150探针2高度复位失败");
                        LogSafeInvokeAppend("3", "G0,150探针2高度复位失败");
                    }
                    Task.Delay(TimeSpan.FromSeconds(1)).Wait();
                    var result3 = heightCheckDriverSJQ.ReHeighStandard("03");
                    if (!result3)
                    {
                        logger.Error("G0,150探针3高度复位失败");
                        LogSafeInvokeAppend("3", "G0,150探针3高度复位失败");
                    }
                    if (result1 && result2 && result3)
                    {
                        var resultfw = plcDriver.WriteShort(PlcPoint.gdfwdz2, 0);
                        if (!resultfw)
                        {
                            logger.Error("G0,150高度复位信号复位失败");
                            LogSafeInvokeAppend("3", "G0,150高度复位信号复位失败");
                        }
                    }
                }
                //获取到位信号
                var state3 = plcDriver.ReadShort(PlcPoint.gdfwdz3);
                if (state3 == -1)
                {
                    logger.Error("G6,250高度复位获取到位信号异常");
                    LogSafeInvokeAppend("3", "G6,250高度复位获取到位信号异常");
                    return;
                }
                else if (state3 > 50)
                {
                    logger.Error("G6,250高度复位与plc连接失败");
                    LogSafeInvokeAppend("3", "G6,250高度复位与plc连接失败");
                    return;
                }
                else if (state3 == 1)
                {
                    logger.Info($"G6,250高度复位获取到位信号{state3}");
                    var result1 = heightCheckDriver.ReHeighStandard("04");
                    if (!result1)
                    {
                        logger.Error("G6,250探针4高度复位失败");
                        LogSafeInvokeAppend("3", "G6,250探针4高度复位失败");
                    }
                    Task.Delay(TimeSpan.FromSeconds(1)).Wait();
                    var result2 = heightCheckDriver.ReHeighStandard("05");
                    if (!result2)
                    {
                        logger.Error("G6,250探针5高度复位失败");
                        LogSafeInvokeAppend("3", "G6,250探针5高度复位失败");
                    }
                    Task.Delay(TimeSpan.FromSeconds(1)).Wait();
                    var result3 = heightCheckDriver.ReHeighStandard("06");
                    if (!result3)
                    {
                        logger.Error("G6,250探针6高度复位失败");
                        LogSafeInvokeAppend("3", "G6,250探针6高度复位失败");
                    }
                    var result4 = heightCheckDriver.ReHeighStandard("07");
                    if (!result4)
                    {
                        logger.Error("G6,250探针7高度复位失败");
                        LogSafeInvokeAppend("3", "G6,250探针7高度复位失败");
                    }
                    if (result1 && result2 && result3 && result4)
                    {
                        var resultfw = plcDriver.WriteShort(PlcPoint.gdfwdz3, 0);
                        if (!resultfw)
                        {
                            logger.Error("G6,250高度复位信号复位失败");
                            LogSafeInvokeAppend("3", "G6,250高度复位信号复位失败");
                        }
                    }
                }
                //获取到位信号
                var state4 = plcDriver.ReadShort(PlcPoint.gdfwdz4);
                if (state4 == -1)
                {
                    logger.Error("G6,150高度复位获取到位信号异常");
                    LogSafeInvokeAppend("3", "G6,150高度复位获取到位信号异常");
                    return;
                }
                else if (state4 > 50)
                {
                    logger.Error("G6,150高度复位与plc连接失败");
                    LogSafeInvokeAppend("3", "G6,150高度复位与plc连接失败");
                    return;
                }
                else if (state4 == 1)
                {
                    logger.Info($"G6,150高度复位获取到位信号{state4}");
                    var result1 = heightCheckDriver.ReHeighStandard("01");
                    if (!result1)
                    {
                        logger.Error("G6,150探针1高度复位失败");
                        LogSafeInvokeAppend("3", "G6,150探针1高度复位失败");
                    }
                    Task.Delay(TimeSpan.FromSeconds(1)).Wait();
                    var result2 = heightCheckDriver.ReHeighStandard("02");
                    if (!result2)
                    {
                        logger.Error("G6,150探针2高度复位失败");
                        LogSafeInvokeAppend("3", "G6,150探针2高度复位失败");
                    }
                    Task.Delay(TimeSpan.FromSeconds(1)).Wait();
                    var result3 = heightCheckDriver.ReHeighStandard("03");
                    if (!result3)
                    {
                        logger.Error("G6,150探针3高度复位失败");
                        LogSafeInvokeAppend("3", "G6,150探针3高度复位失败");
                    }
                    if (result1 && result2 && result3)
                    {
                        var resultfw = plcDriver.WriteShort(PlcPoint.gdfwdz4, 0);
                        if (!resultfw)
                        {
                            logger.Error("G6,150高度复位信号复位失败");
                            LogSafeInvokeAppend("3", "G6,150高度复位信号复位失败");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
        }

        /// <summary>
        /// 获取激光刻印的序号
        /// </summary>
        /// <param name="type"></param>
        /// <param name="datas"></param>
        /// <returns></returns>
        private bool GetLaserEngravingNo(string type, out int data)
        {
            data = 0;
            try
            {
                switch (type)
                {
                    case "A":
                    case "1":
                        data = 1;
                        break;
                    case "B":
                    case "2":
                        data = 2;
                        break;
                    case "C":
                    case "3":
                        data = 3;
                        break;
                    case "D":
                    case "4":
                        data = 4;
                        break;
                    case "E":
                    case "5":
                        data = 5;
                        break;
                    case "F":
                    case "6":
                        data = 6;
                        break;
                    case "G":
                    case "7":
                        data = 7;
                        break;
                    case "H":
                    case "8":
                        data = 8;
                        break;
                    default:
                        return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 打印日志
        /// </summary>
        /// <param name="type"></param>
        /// <param name="message"></param>
        public void LogSafeInvokeAppend(string type, string message)
        {
            try
            {
                switch (type)
                {
                    case "2":
                        _richTextAppendHelper.LogWarning(message);
                        break;
                    case "3":
                        _richTextAppendHelper.LogError(message);
                        break;
                    default:
                        _richTextAppendHelper.LogMessage(message);
                        break;
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
            }
        }

        /// <summary>
        /// 修改高度检测数据展示
        /// </summary>
        /// <param name="datas"></param>
        private bool ChangeHeightData(string no, ref List<string> heightDatas, string state, string heightType, string heightCompensate)
        {
            try
            {

                //产品编号赋值
                uiTextBox1.Invoke(UITextBoxChangeData, uiTextBox1, no);
                if (state == "1")
                {
                    var havePass = true;
                    if (heightType == "1")
                    {
                        heightDatas = heightDatas.Take(3).ToList();
                    }
                    else
                    {
                        heightDatas = heightDatas.Skip(Math.Max(0, heightDatas.Count - 3)).ToList();
                    }
                    var hc = double.Parse(heightCompensate);
                    heightDatas[0] = (double.Parse(heightDatas[0]) + hc).ToString("F3");
                    heightDatas[1] = (double.Parse(heightDatas[1]) + hc).ToString("F3");
                    heightDatas[2] = (double.Parse(heightDatas[2]) + hc).ToString("F3");

                    if (!heightDatas[0].Contains('+') && !heightDatas[0].Contains('-'))
                    {
                        heightDatas[0] = '+' + heightDatas[0];
                    }
                    if (!heightDatas[1].Contains('+') && !heightDatas[1].Contains('-'))
                    {
                        heightDatas[1] = '+' + heightDatas[1];
                    }
                    if (!heightDatas[2].Contains('+') && !heightDatas[2].Contains('-'))
                    {
                        heightDatas[2] = '+' + heightDatas[2];
                    }

                    uiTextBox2.Invoke(UITextBoxChangeData, uiTextBox2, heightDatas[0]);
                    uiTextBox3.Invoke(UITextBoxChangeData, uiTextBox3, heightDatas[1]);
                    uiTextBox4.Invoke(UITextBoxChangeData, uiTextBox4, heightDatas[2]);
                    uiTextBox9.Invoke(UITextBoxChangeData, uiTextBox9, "");
                    uiTextBox10.Invoke(UITextBoxChangeData, uiTextBox10, "");
                    uiTextBox11.Invoke(UITextBoxChangeData, uiTextBox11, "");
                    using (var db = new MyDbContext())
                    {
                        var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                        var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                        if (testindex == null)
                        {
                            logger.Error($"高度检测数据展示产品编号{no}获取物料编号{indexno}信息异常");
                            LogSafeInvokeAppend("3", $"高度检测数据展示产品编号{no}获取物料编号{indexno}信息异常");
                            uiTextBox2.Invoke(UITextBoxChangeFillColor, uiTextBox2, Color.Red);
                            uiTextBox3.Invoke(UITextBoxChangeFillColor, uiTextBox3, Color.Red);
                            uiTextBox4.Invoke(UITextBoxChangeFillColor, uiTextBox4, Color.Red);
                            if (havePass)
                            {
                                havePass = false;
                            }
                        }
                        else
                        {
                            if (testindex.HeightStandard == null || testindex.HeightChangeLimit == null)
                            {
                                logger.Error($"高度检测数据展示产品编号{no}物料编号{indexno}判断标准不能为空");
                                LogSafeInvokeAppend("3", $"高度检测数据展示产品编号{no}物料编号{indexno}判断标准不能为空");
                            }
                            else
                            {
                                var data1 = (double)(double.Parse(heightDatas[0]) - testindex.HeightStandard);
                                var changedata1 = Math.Abs(data1);
                                if (changedata1 > testindex.HeightChangeLimit)
                                {
                                    uiTextBox2.Invoke(UITextBoxChangeFillColor, uiTextBox2, Color.Red);
                                    if (havePass)
                                    {
                                        havePass = false;
                                    }
                                }
                                else
                                {
                                    uiTextBox2.Invoke(UITextBoxChangeFillColor, uiTextBox2, Color.Green);
                                }
                                var data2 = (double)(double.Parse(heightDatas[1]) - testindex.HeightStandard);
                                var changedata2 = Math.Abs(data2);
                                if (changedata2 > testindex.HeightChangeLimit)
                                {
                                    uiTextBox3.Invoke(UITextBoxChangeFillColor, uiTextBox3, Color.Red);
                                    if (havePass)
                                    {
                                        havePass = false;
                                    }
                                }
                                else
                                {
                                    uiTextBox3.Invoke(UITextBoxChangeFillColor, uiTextBox3, Color.Green);
                                }
                                var data3 = (double)(double.Parse(heightDatas[2]) - testindex.HeightStandard);
                                var changedata3 = Math.Abs(data3);
                                if (changedata3 > testindex.HeightChangeLimit)
                                {
                                    uiTextBox4.Invoke(UITextBoxChangeFillColor, uiTextBox4, Color.Red);
                                    if (havePass)
                                    {
                                        havePass = false;
                                    }
                                }
                                else
                                {
                                    uiTextBox4.Invoke(UITextBoxChangeFillColor, uiTextBox4, Color.Green);
                                }
                            }
                        }
                    }

                    return havePass;
                }
                else if (state == "2")
                {
                    var havePass = true;
                    if (heightType == "1")
                    {
                        heightDatas = heightDatas.Take(3).ToList();
                    }
                    else
                    {
                        heightDatas = heightDatas.Skip(Math.Max(0, heightDatas.Count - 3)).ToList();
                    }
                    var hc = double.Parse(heightCompensate);
                    heightDatas[0] = (double.Parse(heightDatas[0]) + hc).ToString("F3");
                    heightDatas[1] = (double.Parse(heightDatas[1]) + hc).ToString("F3");
                    heightDatas[2] = (double.Parse(heightDatas[2]) + hc).ToString("F3");

                    if (!heightDatas[0].Contains('+') && !heightDatas[0].Contains('-'))
                    {
                        heightDatas[0] = '+' + heightDatas[0];
                    }
                    if (!heightDatas[1].Contains('+') && !heightDatas[1].Contains('-'))
                    {
                        heightDatas[1] = '+' + heightDatas[1];
                    }
                    if (!heightDatas[2].Contains('+') && !heightDatas[2].Contains('-'))
                    {
                        heightDatas[2] = '+' + heightDatas[2];
                    }

                    uiTextBox9.Invoke(UITextBoxChangeData, uiTextBox9, heightDatas[0]);
                    uiTextBox10.Invoke(UITextBoxChangeData, uiTextBox10, heightDatas[1]);
                    uiTextBox11.Invoke(UITextBoxChangeData, uiTextBox11, heightDatas[2]);
                    using (var db = new MyDbContext())
                    {
                        var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                        var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                        if (testindex == null)
                        {
                            logger.Error($"高度检测数据展示产品编号{no}获取物料编号{indexno}信息异常");
                            LogSafeInvokeAppend("3", $"高度检测数据展示产品编号{no}获取物料编号{indexno}信息异常");
                            uiTextBox9.Invoke(UITextBoxChangeFillColor, uiTextBox9, Color.Red);
                            uiTextBox10.Invoke(UITextBoxChangeFillColor, uiTextBox10, Color.Red);
                            uiTextBox11.Invoke(UITextBoxChangeFillColor, uiTextBox11, Color.Red);
                            if (havePass)
                            {
                                havePass = false;
                            }
                        }
                        else
                        {
                            if (testindex.HeightStandard == null || testindex.HeightChangeLimit == null)
                            {
                                logger.Error($"高度检测数据展示产品编号{no}物料编号{indexno}判断标准不能为空");
                                LogSafeInvokeAppend("3", $"高度检测数据展示产品编号{no}物料编号{indexno}判断标准不能为空");
                            }
                            else
                            {
                                var data1 = (double)(double.Parse(heightDatas[0]) - testindex.HeightStandard);
                                var changedata1 = Math.Abs(data1);
                                if (changedata1 > testindex.HeightChangeLimit)
                                {
                                    uiTextBox9.Invoke(UITextBoxChangeFillColor, uiTextBox9, Color.Red);
                                    if (havePass)
                                    {
                                        havePass = false;
                                    }
                                }
                                else
                                {
                                    uiTextBox9.Invoke(UITextBoxChangeFillColor, uiTextBox9, Color.Green);
                                }
                                var data2 = (double)(double.Parse(heightDatas[1]) - testindex.HeightStandard);
                                var changedata2 = Math.Abs(data2);
                                if (changedata2 > testindex.HeightChangeLimit)
                                {
                                    uiTextBox10.Invoke(UITextBoxChangeFillColor, uiTextBox10, Color.Red);
                                    if (havePass)
                                    {
                                        havePass = false;
                                    }
                                }
                                else
                                {
                                    uiTextBox10.Invoke(UITextBoxChangeFillColor, uiTextBox10, Color.Green);
                                }
                                var data3 = (double)(double.Parse(heightDatas[2]) - testindex.HeightStandard);
                                var changedata3 = Math.Abs(data3);
                                if (changedata3 > testindex.HeightChangeLimit)
                                {
                                    uiTextBox11.Invoke(UITextBoxChangeFillColor, uiTextBox11, Color.Red);
                                    if (havePass)
                                    {
                                        havePass = false;
                                    }
                                }
                                else
                                {
                                    uiTextBox11.Invoke(UITextBoxChangeFillColor, uiTextBox11, Color.Green);
                                }
                            }
                        }
                    }

                    return havePass;
                }
                return false;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 修改高度检测数据展示
        /// </summary>
        /// <param name="datas"></param>
        private void ChangeHeightData2(string no, ref List<string> heightDatas, string type)
        {
            try
            {
                //产品编号赋值
                uiTextBox18.Invoke(UITextBoxChangeData, uiTextBox18, no);
                if (type == "201" || type == "202")
                {
                    heightDatas = heightDatas.Take(3).ToList();

                    heightDatas[0] = (double.Parse(heightDatas[0])).ToString("F3");
                    heightDatas[1] = (double.Parse(heightDatas[1])).ToString("F3");
                    heightDatas[2] = (double.Parse(heightDatas[2])).ToString("F3");

                    if (!heightDatas[0].Contains('+') && !heightDatas[0].Contains('-'))
                    {
                        heightDatas[0] = '+' + heightDatas[0];
                    }
                    if (!heightDatas[1].Contains('+') && !heightDatas[1].Contains('-'))
                    {
                        heightDatas[1] = '+' + heightDatas[1];
                    }
                    if (!heightDatas[2].Contains('+') && !heightDatas[2].Contains('-'))
                    {
                        heightDatas[2] = '+' + heightDatas[2];
                    }

                    uiTextBox17.Invoke(UITextBoxChangeData, uiTextBox17, heightDatas[0]);
                    uiTextBox16.Invoke(UITextBoxChangeData, uiTextBox16, heightDatas[1]);
                    uiTextBox15.Invoke(UITextBoxChangeData, uiTextBox15, heightDatas[2]);
                    uiTextBox14.Invoke(UITextBoxChangeData, uiTextBox14, "");
                    uiTextBox13.Invoke(UITextBoxChangeData, uiTextBox13, "");
                    uiTextBox12.Invoke(UITextBoxChangeData, uiTextBox12, "");
                    uiTextBox19.Invoke(UITextBoxChangeData, uiTextBox19, "");
                    using (var db = new MyDbContext())
                    {
                        var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                        var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                        if (testindex == null)
                        {
                            logger.Error($"高度检测数据展示产品编号{no}获取物料编号{indexno}信息异常");
                            LogSafeInvokeAppend("3", $"高度检测数据展示产品编号{no}获取物料编号{indexno}信息异常");
                            uiTextBox17.Invoke(UITextBoxChangeFillColor, uiTextBox17, Color.Red);
                            uiTextBox16.Invoke(UITextBoxChangeFillColor, uiTextBox16, Color.Red);
                            uiTextBox15.Invoke(UITextBoxChangeFillColor, uiTextBox15, Color.Red);
                        }
                        else
                        {
                            if (testindex.HeightStandard2 == null || testindex.HeightChangeLimit2 == null)
                            {
                                logger.Error($"高度检测数据展示产品编号{no}物料编号{indexno}判断标准不能为空");
                                LogSafeInvokeAppend("3", $"高度检测数据展示产品编号{no}物料编号{indexno}判断标准不能为空");
                            }
                            else
                            {
                                var data1 = (double)(double.Parse(heightDatas[0]) - testindex.HeightStandard2);
                                var changedata1 = Math.Abs(data1);
                                if (changedata1 > testindex.HeightChangeLimit2)
                                {
                                    uiTextBox17.Invoke(UITextBoxChangeFillColor, uiTextBox17, Color.Red);
                                }
                                else
                                {
                                    uiTextBox17.Invoke(UITextBoxChangeFillColor, uiTextBox17, Color.Green);
                                }
                                var data2 = (double)(double.Parse(heightDatas[1]) - testindex.HeightStandard2);
                                var changedata2 = Math.Abs(data2);
                                if (changedata2 > testindex.HeightChangeLimit2)
                                {
                                    uiTextBox16.Invoke(UITextBoxChangeFillColor, uiTextBox16, Color.Red);
                                }
                                else
                                {
                                    uiTextBox16.Invoke(UITextBoxChangeFillColor, uiTextBox16, Color.Green);
                                }
                                var data3 = (double)(double.Parse(heightDatas[2]) - testindex.HeightStandard2);
                                var changedata3 = Math.Abs(data3);
                                if (changedata3 > testindex.HeightChangeLimit2)
                                {
                                    uiTextBox15.Invoke(UITextBoxChangeFillColor, uiTextBox15, Color.Red);
                                }
                                else
                                {
                                    uiTextBox15.Invoke(UITextBoxChangeFillColor, uiTextBox15, Color.Green);
                                }
                            }
                        }
                    }
                }
                else if (type == "101" || type == "102")
                {
                    heightDatas = heightDatas.Skip(3).ToList();

                    heightDatas[0] = (double.Parse(heightDatas[0])).ToString("F3");
                    heightDatas[1] = (double.Parse(heightDatas[1])).ToString("F3");
                    heightDatas[2] = (double.Parse(heightDatas[2])).ToString("F3");
                    heightDatas[3] = (double.Parse(heightDatas[3])).ToString("F3");

                    if (!heightDatas[0].Contains('+') && !heightDatas[0].Contains('-'))
                    {
                        heightDatas[0] = '+' + heightDatas[0];
                    }
                    if (!heightDatas[1].Contains('+') && !heightDatas[1].Contains('-'))
                    {
                        heightDatas[1] = '+' + heightDatas[1];
                    }
                    if (!heightDatas[2].Contains('+') && !heightDatas[2].Contains('-'))
                    {
                        heightDatas[2] = '+' + heightDatas[2];
                    }
                    if (!heightDatas[3].Contains('+') && !heightDatas[3].Contains('-'))
                    {
                        heightDatas[3] = '+' + heightDatas[3];
                    }

                    uiTextBox17.Invoke(UITextBoxChangeData, uiTextBox17, "");
                    uiTextBox16.Invoke(UITextBoxChangeData, uiTextBox16, "");
                    uiTextBox15.Invoke(UITextBoxChangeData, uiTextBox15, "");
                    uiTextBox14.Invoke(UITextBoxChangeData, uiTextBox14, heightDatas[0]);
                    uiTextBox13.Invoke(UITextBoxChangeData, uiTextBox13, heightDatas[1]);
                    uiTextBox12.Invoke(UITextBoxChangeData, uiTextBox12, heightDatas[2]);
                    uiTextBox19.Invoke(UITextBoxChangeData, uiTextBox19, heightDatas[3]);
                    using (var db = new MyDbContext())
                    {
                        var indexno = plcDriver.ReadShort(PlcPoint.MaterielNo);
                        var testindex = db.Set<TestIndex>().FirstOrDefault(x => x.IndexNum == indexno.ToString());
                        if (testindex == null)
                        {
                            logger.Error($"高度检测数据展示产品编号{no}获取物料编号{indexno}信息异常");
                            LogSafeInvokeAppend("3", $"高度检测数据展示产品编号{no}获取物料编号{indexno}信息异常");
                            uiTextBox14.Invoke(UITextBoxChangeFillColor, uiTextBox14, Color.Red);
                            uiTextBox13.Invoke(UITextBoxChangeFillColor, uiTextBox13, Color.Red);
                            uiTextBox12.Invoke(UITextBoxChangeFillColor, uiTextBox12, Color.Red);
                            uiTextBox19.Invoke(UITextBoxChangeFillColor, uiTextBox19, Color.Red);
                        }
                        else
                        {
                            if (testindex.HeightStandard2 == null || testindex.HeightChangeLimit2 == null || 
                                testindex.HeightStandard3 == null || testindex.HeightChangeLimit3 == null)
                            {
                                logger.Error($"高度检测数据展示产品编号{no}物料编号{indexno}判断标准不能为空");
                                LogSafeInvokeAppend("3", $"高度检测数据展示产品编号{no}物料编号{indexno}判断标准不能为空");
                            }
                            else
                            {
                                var data1 = (double)(double.Parse(heightDatas[0]) - testindex.HeightStandard3);
                                var changedata1 = Math.Abs(data1);
                                if (changedata1 > testindex.HeightChangeLimit3)
                                {
                                    uiTextBox14.Invoke(UITextBoxChangeFillColor, uiTextBox14, Color.Red);
                                }
                                else
                                {
                                    uiTextBox14.Invoke(UITextBoxChangeFillColor, uiTextBox14, Color.Green);
                                }
                                var data2 = (double)(double.Parse(heightDatas[1]) - testindex.HeightStandard2);
                                var changedata2 = Math.Abs(data2);
                                if (changedata2 > testindex.HeightChangeLimit2)
                                {
                                    uiTextBox13.Invoke(UITextBoxChangeFillColor, uiTextBox13, Color.Red);
                                }
                                else
                                {
                                    uiTextBox13.Invoke(UITextBoxChangeFillColor, uiTextBox13, Color.Green);
                                }
                                var data3 = (double)(double.Parse(heightDatas[2]) - testindex.HeightStandard2);
                                var changedata3 = Math.Abs(data3);
                                if (changedata3 > testindex.HeightChangeLimit2)
                                {
                                    uiTextBox12.Invoke(UITextBoxChangeFillColor, uiTextBox12, Color.Red);
                                }
                                else
                                {
                                    uiTextBox12.Invoke(UITextBoxChangeFillColor, uiTextBox12, Color.Green);
                                }
                                var data4 = (double)(double.Parse(heightDatas[3]) - testindex.HeightStandard2);
                                var changedata4 = Math.Abs(data4);
                                if (changedata4 > testindex.HeightChangeLimit2)
                                {
                                    uiTextBox19.Invoke(UITextBoxChangeFillColor, uiTextBox19, Color.Red);
                                }
                                else
                                {
                                    uiTextBox19.Invoke(UITextBoxChangeFillColor, uiTextBox19, Color.Green);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                LogSafeInvokeAppend("3", ex.Message);
            }
        }

        /// <summary>
        /// 初始化datagridview1
        /// </summary>
        private void InitDataGridView1()
        {
            uiDataGridView1.AddColumn("样品编号", "SampleNo");
            uiDataGridView1.AddColumn("高度数据1", "HeightData1");
            uiDataGridView1.AddColumn("高度数据2", "HeightData2");
            uiDataGridView1.AddColumn("绝缘数据", "IrData");
            uiDataGridView1.AddColumn("绝缘结论", "IrResult");
            //uiDataGridView1.AddColumn("识别字符", "CheckBit");
            uiDataGridView1.AddColumn("圆直径", "CircleDiameter1");
            uiDataGridView1.AddColumn("圆直径结论", "CircleDiameterRes1");
            uiDataGridView1.AddColumn("圆直径2", "CircleDiameter2");
            uiDataGridView1.AddColumn("圆直径2结论", "CircleDiameterRes2");
            uiDataGridView1.AddColumn("外观数据", "AppearanceData");
            uiDataGridView1.AddColumn("毛边", "MBResult");
            uiDataGridView1.AddColumn("外观结论", "AppearanceResult");

            //uiDataGridView1.AddColumn("蚀刻机结论", "EtchingMachineResult");
            uiDataGridView1.AddColumn("二维码数据", "TDCodeData");
            uiDataGridView1.AddColumn("二维码等级", "TDCodeLimit");
            uiDataGridView1.AddColumn("二维码结论", "TDCodeResult");

            uiDataGridView1.AddColumn("高度数据3", "HeightData3");
            uiDataGridView1.AddColumn("同轴度结论", "ConcentricityResult");
            uiDataGridView1.AddColumn("创建时间", "CreateTime");
            uiDataGridView1.AddColumn("总结论", "Conclusion");

            uiDataGridView1.Columns["二维码数据"].Width = 270;
            uiDataGridView1.Columns["高度数据1"].Width = 370;
            uiDataGridView1.Columns["高度数据2"].Width = 370;
            uiDataGridView1.Columns["高度数据3"].Width = 370;
            uiDataGridView1.Columns["创建时间"].Width = 230;
            uiDataGridView1.AutoGenerateColumns = false;
            uiDataGridView1.ReadOnly = true;
        }
        /// <summary>
        /// 数据展示
        /// </summary>
        private void DataPresentation()
        {
            try
            {
                using (var db = new MyDbContext())
                {
                    uiDataGridView1.Invoke(UIDataGridViewChangeData, uiDataGridView1, null);
                    //uiDataGridView1.DataSource = null;
                    DateTime todayStart = DateTime.Today;
                    var datas = db.Set<TestResult>().Where(x => x.CreateTime >= todayStart).OrderByDescending(x => x.CreateTime).ToList();
                    if (datas.Any())
                    {
                        //uiDataGridView1.DataSource = datas;
                        uiDataGridView1.Invoke(UIDataGridViewChangeData, uiDataGridView1, datas);
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
            }
        }

        #region 触发器

        Action<UITextBox, string> UITextBoxChangeData = (x, y) => { x.Text = y; };
        Action<UITextBox, Color> UITextBoxChangeFillColor = (x, y) => { x.FillColor = y; };
        Action<UILabel, Color> UILabelChangeColor = (x, y) =>
        {
            x.ForeColor = y;
        };
        Action<UIDataGridView, object> UIDataGridViewChangeData = (x, y) => { x.DataSource = y; };
        Action<UIButton, Color> UIButtonChangeFillColor = (x, y) => { x.FillColor = y; };

        #endregion

        public void test()
        {
            try
            {
                //var v = new SaveFileData()
                //{
                //    SampleNo = "1",
                //    SmallCircle1 = "2",
                //    SmallCircle2 = "3",
                //    SmallCircle3 = "4",
                //    SmallCircle4 = "5",
                //    GreatCircleOD1 = "6",
                //    GreatCircleOD2 = "7",
                //    GreatCircleID1 = "8",
                //    //CheckBit = "9",
                //};
                //fileHelper.SaveMeasuredValues(runConSetting.SaveFileAdd, runConSetting.SaveFileName, v);
                //fileHelper.GetMeasuredValues(runConSetting.UpPictureFileAdd, "1", out UpFileData value);
                //fileHelper.GetMeasuredValues(runConSetting.DownPictureFileAdd, "1", out DownFileData value2);

                //fileHelper.MoveFile(runConSetting.UpPictureFileAdd, null);
                //using (var db = new MyDbContext())
                //{
                //    var a = db.Set<TestResult>().ToList();

                //    var data = new TestResult()
                //    {
                //        Id = Guid.NewGuid().ToString("N"),
                //    };

                //    db.Set<TestResult>().Add(data);
                //    db.SaveChanges();
                //}
            }
            catch (Exception ex)
            {
                logger.Error(ex);
            }
        }

        public void test2()
        {
            try
            {
                plcDriver.Connect();
                var re = plcDriver.ReadBool(1, 1);
                var re2 = plcDriver.ReadInt(39);
                var re3 = plcDriver.ReadShort(39);

                var re4 = plcDriver.WriteBool(39, false);
                var re5 = plcDriver.WriteInt(39, 1);
                var re6 = plcDriver.WriteShort(39, 1);
            }
            catch (Exception ex)
            {
                logger.Error(ex);
            }
        }



        #region 系统设置

        /// <summary>
        /// 编号归零
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton8_Click(object sender, EventArgs e)
        {
            try
            {
                var res = UIMessageBox.ShowAsk("是否重置样品编号");
                if (res)
                {
                    var plcre = plcDriver.WriteShort(PlcPoint.bhql, 1);
                    if (plcre)
                    {
                        UIMessageBox.ShowAsk("调用成功");
                    }
                    else
                    {
                        UIMessageBox.ShowError("调用失败");
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        private void InitButton()
        {
            try
            {
                if (runConSetting.IssueStatus == "1")
                {
                    uiButton9.Invoke(UIButtonChangeFillColor, uiButton9, Color.Green);
                    uiButton1.Invoke(UIButtonChangeFillColor, uiButton1, Color.Gray);
                }
                else
                {
                    uiButton9.Invoke(UIButtonChangeFillColor, uiButton9, Color.Gray);
                    uiButton1.Invoke(UIButtonChangeFillColor, uiButton1, Color.Green);
                }
                uiComboBox2.Text = runConSetting.EncodingData;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
            }
        }

        /// <summary>
        /// 自动下发
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton9_Click(object sender, EventArgs e)
        {
            try
            {
                runConSetting.IssueStatus = "1";
                runConSetting.Save();

                uiButton9.Invoke(UIButtonChangeFillColor, uiButton9, Color.Green);
                uiButton1.Invoke(UIButtonChangeFillColor, uiButton1, Color.Gray);
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 手动下发
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton1_Click(object sender, EventArgs e)
        {
            try
            {
                runConSetting.IssueStatus = "2";
                runConSetting.EncodingData = uiComboBox2.Text;
                runConSetting.Save();

                uiButton9.Invoke(UIButtonChangeFillColor, uiButton9, Color.Gray);
                uiButton1.Invoke(UIButtonChangeFillColor, uiButton1, Color.Green);
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        private void InitSystemControl()
        {
            try
            {
                if (runConSetting.PassWGJC == "true")
                {
                    uiCheckBox1.Checked = true;
                }
                else
                {
                    uiCheckBox1.Checked = false;
                }

                if (runConSetting.PassCFBM == "true")
                {
                    uiCheckBox2.Checked = true;
                }
                else
                {
                    uiCheckBox2.Checked = false;
                }

                if (runConSetting.GDJC == "true")
                {
                    uiCheckBox3.Checked = true;
                }
                else
                {
                    uiCheckBox3.Checked = false;
                }
                if (runConSetting.WGJC == "true")
                {
                    uiCheckBox4.Checked = true;
                }
                else
                {
                    uiCheckBox4.Checked = false;
                }
                if (runConSetting.NYJC == "true")
                {
                    uiCheckBox5.Checked = true;
                }
                else
                {
                    uiCheckBox5.Checked = false;
                }
                if (runConSetting.SKJKY == "true")
                {
                    uiCheckBox6.Checked = true;
                }
                else
                {
                    uiCheckBox6.Checked = false;
                }
                if (runConSetting.EWMJC == "true")
                {
                    uiCheckBox7.Checked = true;
                }
                else
                {
                    uiCheckBox7.Checked = false;
                }
                if (runConSetting.TZDJC == "true")
                {
                    uiCheckBox8.Checked = true;
                }
                else
                {
                    uiCheckBox8.Checked = false;
                }
                if (runConSetting.CFBHJC == "true")
                {
                    uiCheckBox9.Checked = true;
                }
                else
                {
                    uiCheckBox9.Checked = false;
                }


                uiTextBox31.Text = runConSetting.SaveFileAdd;
                using (var db = new MyDbContext())
                {
                    var names = db.Set<TestIndex>().Select(x => x.SampleName).ToList();
                    uiComboBox3.Text = null;
                    uiComboBox3.DataSource = null;
                    uiComboBox3.DataSource = names;

                    nySetDatas = db.Set<NYSetData>().ToList();
                }

                uiComboBox4.Text = runConSetting.KMJudgeLimit;

                // 绑定事件
                uiRadioButton1.CheckedChanged += RadioButton_CheckedChanged;
                uiRadioButton2.CheckedChanged += RadioButton_CheckedChanged;

                if (runConSetting.TestItem == 1)
                {
                    uiRadioButton1.Checked = true;
                }
                else if (runConSetting.TestItem == 2)
                {
                    uiRadioButton2.Checked = true;
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
            }
        }

        private void RadioButton_CheckedChanged(object sender, EventArgs e)
        {
            if (uiRadioButton1.Checked)
            {
                runConSetting.TestItem = 1;
                runConSetting.Save();
            }
            else if (uiRadioButton2.Checked)
            {
                runConSetting.TestItem = 2;
                runConSetting.Save();
            }
        }

        private void uiCheckBox1_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                var data = uiCheckBox1.Checked ? "true" : "false";
                runConSetting.PassWGJC = data;
                runConSetting.Save();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        private void uiCheckBox2_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                var data = uiCheckBox2.Checked ? "true" : "false";
                runConSetting.PassCFBM = data;
                runConSetting.Save();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 重复编号认证
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiCheckBox9_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                var data = uiCheckBox9.Checked ? "true" : "false";
                runConSetting.CFBHJC = data;
                runConSetting.Save();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton7_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(uiComboBox3.Text) || string.IsNullOrEmpty(uiTextBox23.Text) || string.IsNullOrEmpty(uiTextBox24.Text) || string.IsNullOrEmpty(uiComboBox1.Text) ||
                    string.IsNullOrEmpty(uiTextBox26.Text) || string.IsNullOrEmpty(uiTextBox27.Text) || string.IsNullOrEmpty(uiTextBox28.Text) ||
                    string.IsNullOrEmpty(uiTextBox5.Text) || string.IsNullOrEmpty(uiTextBox25.Text) || string.IsNullOrEmpty(uiTextBox29.Text) || string.IsNullOrEmpty(uiTextBox30.Text) ||
                    string.IsNullOrEmpty(uiTextBox32.Text) || string.IsNullOrEmpty(uiComboBox5.Text))
                {
                    UIMessageBox.ShowError("选项不能为空");
                    return;
                }
                var ask = UIMessageBox.ShowAsk("是否保存当前数据");
                if (ask)
                {
                    using (var db = new MyDbContext())
                    {
                        if (nySetData == null)
                        {
                            //绝缘电阻
                            var nyyVoltage = ushort.Parse(uiTextBox24.Text);
                            ushort irRange = 0;
                            switch (uiComboBox1.Text)
                            {
                                case "AUTO":
                                    irRange = 0;
                                    break;
                                case "100G":
                                    irRange = 1;
                                    break;
                                case "300M":
                                    irRange = 2;
                                    break;
                                case "30M":
                                    irRange = 3;
                                    break;
                                case "3M":
                                    irRange = 4;
                                    break;
                                case "100K":
                                    irRange = 5;
                                    break;
                                default:
                                    irRange = 0;
                                    break;
                            }
                            var irUpperLimit = int.Parse(uiTextBox26.Text);
                            var irLowerLimit = int.Parse(uiTextBox27.Text);
                            var irTestTime = double.Parse(uiTextBox28.Text);
                            //直流电流
                            var dcVoltage = ushort.Parse(uiTextBox5.Text);
                            var dcUpperLimit = double.Parse(uiTextBox25.Text);
                            var dcLowerLimit = double.Parse(uiTextBox29.Text);
                            var dcRiseTime = double.Parse(uiTextBox30.Text);
                            var dcTestTime = double.Parse(uiTextBox32.Text);
                            var dcSensitivity = ushort.Parse(uiComboBox5.Text);
                            var nydata = new NYSetData()
                            {
                                Id = Guid.NewGuid().ToString("N"),
                                SampleName = uiComboBox3.Text,
                                NYYPort = uiTextBox23.Text,
                                NyyVoltage = nyyVoltage,
                                IrRange = irRange,
                                IrUpperLimit = irUpperLimit,
                                IrLowerLimit = irLowerLimit,
                                IrTestTime = irTestTime,
                                DcVoltage = dcVoltage,
                                DcUpperLimit = dcUpperLimit,
                                DcLowerLimit = dcLowerLimit,
                                DcRiseTime = dcRiseTime,
                                DcSensitivity = dcSensitivity,
                                DcTestTime = dcTestTime,

                            };
                            lock (dataSaveObject)
                            {
                                db.Set<NYSetData>().Add(nydata);
                                db.SaveChanges();
                            }
                        }
                        else
                        {
                            nySetData.NYYPort = uiTextBox23.Text;
                            var nyyVoltage = ushort.Parse(uiTextBox24.Text);
                            nySetData.NyyVoltage = nyyVoltage;
                            switch (uiComboBox1.Text)
                            {
                                case "AUTO":
                                    nySetData.IrRange = 0;
                                    break;
                                case "100G":
                                    nySetData.IrRange = 1;
                                    break;
                                case "300M":
                                    nySetData.IrRange = 2;
                                    break;
                                case "30M":
                                    nySetData.IrRange = 3;
                                    break;
                                case "3M":
                                    nySetData.IrRange = 4;
                                    break;
                                case "100K":
                                    nySetData.IrRange = 5;
                                    break;
                                default:
                                    nySetData.IrRange = 0;
                                    break;
                            }
                            var irUpperLimit = int.Parse(uiTextBox26.Text);
                            nySetData.IrUpperLimit = irUpperLimit;
                            var irLowerLimit = int.Parse(uiTextBox27.Text);
                            nySetData.IrLowerLimit = irLowerLimit;
                            var irTestTime = double.Parse(uiTextBox28.Text);
                            nySetData.IrTestTime = irTestTime;
                            //直流电流
                            var dcVoltage = ushort.Parse(uiTextBox5.Text);
                            nySetData.DcVoltage = dcVoltage;
                            var dcUpperLimit = double.Parse(uiTextBox25.Text);
                            nySetData.DcUpperLimit = dcUpperLimit;
                            var dcLowerLimit = double.Parse(uiTextBox29.Text);
                            nySetData.DcLowerLimit = dcLowerLimit;
                            var dcRiseTime = double.Parse(uiTextBox30.Text);
                            nySetData.DcRiseTime = dcRiseTime;
                            var dcTestTime = double.Parse(uiTextBox32.Text);
                            nySetData.DcTestTime = dcTestTime;
                            var dcSensitivity = ushort.Parse(uiComboBox5.Text);
                            nySetData.DcSensitivity = dcSensitivity;
                            lock (dataSaveObject)
                            {
                                db.Set<NYSetData>().Update(nySetData);
                                db.SaveChanges();
                            }
                        }
                        nySetDatas = db.Set<NYSetData>().ToList();
                    }

                    runConSetting.NYYPort = uiTextBox23.Text;
                    runConSetting.Save();
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 选择目录
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton2_Click(object sender, EventArgs e)
        {
            try
            {
                FolderBrowserDialog dialog = new FolderBrowserDialog();
                dialog.Description = "选择路径"; ;//左上角提示
                string path = string.Empty;

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    path = dialog.SelectedPath;//获取选中文件路径
                    uiTextBox31.Text = path;
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }
        /// <summary>
        /// 保存目录
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton3_Click(object sender, EventArgs e)
        {
            try
            {
                runConSetting.SaveFileAdd = uiTextBox31.Text;
                runConSetting.Save();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 耐压样品改变参数设置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiComboBox3_TextChanged(object sender, EventArgs e)
        {
            try
            {
                nySetData = null;
                uiTextBox23.Text = null;
                uiTextBox24.Text = null;
                uiComboBox1.Text = null;
                uiTextBox26.Text = null;
                uiTextBox27.Text = null;
                uiTextBox28.Text = null;
                //直流电流
                uiTextBox5.Text = null;
                uiTextBox25.Text = null;
                uiTextBox29.Text = null;
                uiTextBox30.Text = null;
                uiTextBox32.Text = null;
                uiComboBox5.Text = null;
                using (var db = new MyDbContext())
                {
                    var nysetdata = db.Set<NYSetData>().Where(x => x.SampleName == uiComboBox3.Text).FirstOrDefault();
                    if (nysetdata != null)
                    {
                        nySetData = nysetdata;
                        uiTextBox23.Text = nySetData.NYYPort;
                        uiTextBox24.Text = nySetData.NyyVoltage.ToString();
                        switch (nySetData.IrRange)
                        {
                            case 0:
                                uiComboBox1.Text = "AUTO";
                                break;
                            case 1:
                                uiComboBox1.Text = "100G";
                                break;
                            case 2:
                                uiComboBox1.Text = "300M";
                                break;
                            case 3:
                                uiComboBox1.Text = "30M";
                                break;
                            case 4:
                                uiComboBox1.Text = "3M";
                                break;
                            case 5:
                                uiComboBox1.Text = "100K";
                                break;
                            default:
                                break;
                        }
                        uiTextBox26.Text = nySetData.IrUpperLimit.ToString();
                        uiTextBox27.Text = nySetData.IrLowerLimit.ToString();
                        uiTextBox28.Text = nySetData.IrTestTime.ToString();
                        //直流电流
                        uiTextBox5.Text = nySetData.DcVoltage.ToString();
                        uiTextBox25.Text = nySetData.DcUpperLimit.ToString();
                        uiTextBox29.Text = nySetData.DcLowerLimit.ToString();
                        uiTextBox30.Text = nySetData.DcRiseTime.ToString();
                        uiTextBox32.Text = nySetData.DcTestTime.ToString();
                        uiComboBox5.Text = nySetData.DcSensitivity.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 保存蚀刻等级
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton10_Click(object sender, EventArgs e)
        {
            try
            {
                runConSetting.KMJudgeLimit = uiComboBox4.Text;
                runConSetting.Save();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        /// <summary>
        /// 外观前高度读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton4_Click(object sender, EventArgs e)
        {
            try
            {
                uiTextBox2.Invoke(UITextBoxChangeData, uiTextBox2, "");
                uiTextBox3.Invoke(UITextBoxChangeData, uiTextBox3, "");
                uiTextBox4.Invoke(UITextBoxChangeData, uiTextBox4, "");
                uiTextBox9.Invoke(UITextBoxChangeData, uiTextBox9, "");
                uiTextBox10.Invoke(UITextBoxChangeData, uiTextBox10, "");
                uiTextBox11.Invoke(UITextBoxChangeData, uiTextBox11, "");
                var readres = heightCheckDriverSJQ.ReadHeightData(out List<string> heightDatas);
                uiTextBox2.Invoke(UITextBoxChangeData, uiTextBox2, heightDatas[0]);
                uiTextBox3.Invoke(UITextBoxChangeData, uiTextBox3, heightDatas[1]);
                uiTextBox4.Invoke(UITextBoxChangeData, uiTextBox4, heightDatas[2]);
                uiTextBox9.Invoke(UITextBoxChangeData, uiTextBox9, heightDatas[3]);
                uiTextBox10.Invoke(UITextBoxChangeData, uiTextBox10, heightDatas[4]);
                uiTextBox11.Invoke(UITextBoxChangeData, uiTextBox11, heightDatas[5]);
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }
        /// <summary>
        /// 高度读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton5_Click(object sender, EventArgs e)
        {
            try
            {
                uiTextBox17.Invoke(UITextBoxChangeData, uiTextBox17, "");
                uiTextBox16.Invoke(UITextBoxChangeData, uiTextBox16, "");
                uiTextBox15.Invoke(UITextBoxChangeData, uiTextBox15, "");
                uiTextBox14.Invoke(UITextBoxChangeData, uiTextBox14, "");
                uiTextBox13.Invoke(UITextBoxChangeData, uiTextBox13, "");
                uiTextBox12.Invoke(UITextBoxChangeData, uiTextBox12, "");
                uiTextBox19.Invoke(UITextBoxChangeData, uiTextBox19, "");
                var readres = heightCheckDriver.ReadHeightData(out List<string> heightDatas);
                uiTextBox17.Invoke(UITextBoxChangeData, uiTextBox17, heightDatas[0]);
                uiTextBox16.Invoke(UITextBoxChangeData, uiTextBox16, heightDatas[1]);
                uiTextBox15.Invoke(UITextBoxChangeData, uiTextBox15, heightDatas[2]);
                uiTextBox14.Invoke(UITextBoxChangeData, uiTextBox14, heightDatas[3]);
                uiTextBox13.Invoke(UITextBoxChangeData, uiTextBox13, heightDatas[4]);
                uiTextBox12.Invoke(UITextBoxChangeData, uiTextBox12, heightDatas[5]);
                uiTextBox19.Invoke(UITextBoxChangeData, uiTextBox19, heightDatas[6]);
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }
        /// <summary>
        /// 二维码读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton6_Click(object sender, EventArgs e)
        {
            try
            {
                //读取二维码
                var ewmres = ewmDriver.TakePicture(out string value, out string limit);
                uiTextBox21.Invoke(UITextBoxChangeData, uiTextBox21, value.ToString());
                uiTextBox20.Invoke(UITextBoxChangeData, uiTextBox20, limit.ToString());
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 总结论判定
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton11_Click(object sender, EventArgs e)
        {
            try
            {
                if (uiCheckBox3.Checked == true)
                {
                    runConSetting.GDJC = "true";

                }
                else
                {
                    runConSetting.GDJC = "false";
                }
                if (uiCheckBox4.Checked == true)
                {
                    runConSetting.WGJC = "true";

                }
                else
                {
                    runConSetting.WGJC = "false";
                }
                if (uiCheckBox5.Checked == true)
                {
                    runConSetting.NYJC = "true";

                }
                else
                {
                    runConSetting.NYJC = "false";
                }
                if (uiCheckBox6.Checked == true)
                {
                    runConSetting.SKJKY = "true";

                }
                else
                {
                    runConSetting.SKJKY = "false";
                }
                if (uiCheckBox7.Checked == true)
                {
                    runConSetting.EWMJC = "true";

                }
                else
                {
                    runConSetting.EWMJC = "false";
                }
                if (uiCheckBox8.Checked == true)
                {
                    runConSetting.TZDJC = "true";

                }
                else
                {
                    runConSetting.TZDJC = "false";
                }
                runConSetting.Save();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }


    }
}
