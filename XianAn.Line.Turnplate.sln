﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.10.35027.167
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "XianAn.Line.Turnplate", "XianAn.Line.Turnplate\XianAn.Line.Turnplate.csproj", "{06260E87-450C-4621-82AF-C5634B6170A1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Drivers", "Drivers", "{350762E0-CD78-424F-85E2-B14808F0A886}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "XianAn.Line.Turnplate.Dal", "XianAn.Line.Turnplate.Dal\XianAn.Line.Turnplate.Dal.csproj", "{DF2EA7CE-CFF7-41CA-830F-6C120721D20E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "XianAn.Line.Turnplate.UnityHelper", "XianAn.Line.Turnplate.UnityHelper\XianAn.Line.Turnplate.UnityHelper.csproj", "{AC0142A2-41D9-4D55-8FC8-573507CCA9C8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "XianAn.Driver.CameraJNS", "XianAn.Driver.CameraJNS\XianAn.Driver.CameraJNS.csproj", "{35B88537-E4BC-440E-9A0A-9C7EAFE42026}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "XianA.Driver.EtchingMachine", "XianA.Driver.EtchingMachine\XianA.Driver.EtchingMachine.csproj", "{1FFFD873-A1DF-4924-AD3C-239655439287}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "XianAn.Driver.PLC", "XianAn.Driver.PLC\XianAn.Driver.PLC.csproj", "{41803F78-AF9C-4827-BA2C-66C7978ABE80}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XianAn.Driver.CameraEWM", "XianAn.Driver.CameraEWM\XianAn.Driver.CameraEWM.csproj", "{443DF1CA-54DA-41A9-91BF-D1830AE8E90B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XianAn.Driver.HeightCheck", "XianAn.Driver.HeightCheck\XianAn.Driver.HeightCheck.csproj", "{328E22AC-564A-43A3-BED8-30D54E7F4C3F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XianA.Driver.EtchingMachineJPT", "XianA.Driver.EtchingMachineJPT\XianA.Driver.EtchingMachineJPT.csproj", "{29AE8359-EE94-4A72-8981-C8BF439A9C0F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XianAn.Driver.NY", "XianAn.Driver.NY\XianAn.Driver.NY.csproj", "{EAADD7BE-2841-4BED-99C2-3766C8B4F20E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{06260E87-450C-4621-82AF-C5634B6170A1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{06260E87-450C-4621-82AF-C5634B6170A1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{06260E87-450C-4621-82AF-C5634B6170A1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{06260E87-450C-4621-82AF-C5634B6170A1}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF2EA7CE-CFF7-41CA-830F-6C120721D20E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF2EA7CE-CFF7-41CA-830F-6C120721D20E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF2EA7CE-CFF7-41CA-830F-6C120721D20E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF2EA7CE-CFF7-41CA-830F-6C120721D20E}.Release|Any CPU.Build.0 = Release|Any CPU
		{AC0142A2-41D9-4D55-8FC8-573507CCA9C8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AC0142A2-41D9-4D55-8FC8-573507CCA9C8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AC0142A2-41D9-4D55-8FC8-573507CCA9C8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AC0142A2-41D9-4D55-8FC8-573507CCA9C8}.Release|Any CPU.Build.0 = Release|Any CPU
		{35B88537-E4BC-440E-9A0A-9C7EAFE42026}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{35B88537-E4BC-440E-9A0A-9C7EAFE42026}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{35B88537-E4BC-440E-9A0A-9C7EAFE42026}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{35B88537-E4BC-440E-9A0A-9C7EAFE42026}.Release|Any CPU.Build.0 = Release|Any CPU
		{1FFFD873-A1DF-4924-AD3C-239655439287}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1FFFD873-A1DF-4924-AD3C-239655439287}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1FFFD873-A1DF-4924-AD3C-239655439287}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1FFFD873-A1DF-4924-AD3C-239655439287}.Release|Any CPU.Build.0 = Release|Any CPU
		{41803F78-AF9C-4827-BA2C-66C7978ABE80}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{41803F78-AF9C-4827-BA2C-66C7978ABE80}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{41803F78-AF9C-4827-BA2C-66C7978ABE80}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{41803F78-AF9C-4827-BA2C-66C7978ABE80}.Release|Any CPU.Build.0 = Release|Any CPU
		{443DF1CA-54DA-41A9-91BF-D1830AE8E90B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{443DF1CA-54DA-41A9-91BF-D1830AE8E90B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{443DF1CA-54DA-41A9-91BF-D1830AE8E90B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{443DF1CA-54DA-41A9-91BF-D1830AE8E90B}.Release|Any CPU.Build.0 = Release|Any CPU
		{328E22AC-564A-43A3-BED8-30D54E7F4C3F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{328E22AC-564A-43A3-BED8-30D54E7F4C3F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{328E22AC-564A-43A3-BED8-30D54E7F4C3F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{328E22AC-564A-43A3-BED8-30D54E7F4C3F}.Release|Any CPU.Build.0 = Release|Any CPU
		{29AE8359-EE94-4A72-8981-C8BF439A9C0F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{29AE8359-EE94-4A72-8981-C8BF439A9C0F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{29AE8359-EE94-4A72-8981-C8BF439A9C0F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{29AE8359-EE94-4A72-8981-C8BF439A9C0F}.Release|Any CPU.Build.0 = Release|Any CPU
		{EAADD7BE-2841-4BED-99C2-3766C8B4F20E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EAADD7BE-2841-4BED-99C2-3766C8B4F20E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EAADD7BE-2841-4BED-99C2-3766C8B4F20E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EAADD7BE-2841-4BED-99C2-3766C8B4F20E}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{35B88537-E4BC-440E-9A0A-9C7EAFE42026} = {350762E0-CD78-424F-85E2-B14808F0A886}
		{1FFFD873-A1DF-4924-AD3C-239655439287} = {350762E0-CD78-424F-85E2-B14808F0A886}
		{41803F78-AF9C-4827-BA2C-66C7978ABE80} = {350762E0-CD78-424F-85E2-B14808F0A886}
		{443DF1CA-54DA-41A9-91BF-D1830AE8E90B} = {350762E0-CD78-424F-85E2-B14808F0A886}
		{328E22AC-564A-43A3-BED8-30D54E7F4C3F} = {350762E0-CD78-424F-85E2-B14808F0A886}
		{29AE8359-EE94-4A72-8981-C8BF439A9C0F} = {350762E0-CD78-424F-85E2-B14808F0A886}
		{EAADD7BE-2841-4BED-99C2-3766C8B4F20E} = {350762E0-CD78-424F-85E2-B14808F0A886}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {37B1E83F-F84A-4816-A67D-0EB8E17BAA5E}
	EndGlobalSection
EndGlobal
