﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XianAn.Line.Turnplate.Dal.Model
{
    public class NYSetData
    {
        public string Id { get; set; }
        /// <summary>
        /// 样品名称
        /// </summary>
        public string? SampleName { get; set; }
        /// <summary>
        /// 耐压仪端口
        /// </summary>
        public string? NYYPort { get; set; }
        /// <summary>
        /// 耐压仪试验电压
        /// </summary>
        public ushort? NyyVoltage { get; set; }
        /// <summary>
        /// 耐压仪电阻量程
        /// </summary>
        public ushort? IrRange { get; set; }
        /// <summary>
        /// 耐压仪电阻上限
        /// </summary>
        public int? IrUpperLimit { get; set; }
        /// <summary>
        /// 耐压仪电阻下限
        /// </summary>
        public int? IrLowerLimit { get; set; }
        /// <summary>
        /// 耐压仪测试时间
        /// </summary>
        public double? IrTestTime { get; set; }


        /// <summary>
        /// 直流电流试验电压
        /// </summary>
        public ushort? DcVoltage { get; set; }
        /// <summary>
        /// 直流电流上限
        /// </summary>
        public double? DcUpperLimit { get; set; }
        /// <summary>
        /// 直流电流下限
        /// </summary>
        public double? DcLowerLimit { get; set; }
        /// <summary>
        /// 直流电流上升时间
        /// </summary>
        public double? DcRiseTime { get; set; }
        /// <summary>
        /// 直流电流测试时间
        /// </summary>
        public double? DcTestTime { get; set; }
        /// <summary>
        /// 直流电流灵敏度
        /// </summary>
        public ushort? DcSensitivity { get; set; }
    }
}
