﻿namespace XianAn.Line.Turnplate
{
    partial class MainControl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            uiGroupBox1 = new Sunny.UI.UIGroupBox();
            uiButton4 = new Sunny.UI.UIButton();
            uiTextBox11 = new Sunny.UI.UITextBox();
            uiLabel10 = new Sunny.UI.UILabel();
            uiTextBox10 = new Sunny.UI.UITextBox();
            uiLabel9 = new Sunny.UI.UILabel();
            uiTextBox9 = new Sunny.UI.UITextBox();
            uiLabel5 = new Sunny.UI.UILabel();
            uiTextBox4 = new Sunny.UI.UITextBox();
            uiLabel4 = new Sunny.UI.UILabel();
            uiTextBox3 = new Sunny.UI.UITextBox();
            uiLabel3 = new Sunny.UI.UILabel();
            uiTextBox2 = new Sunny.UI.UITextBox();
            uiLabel2 = new Sunny.UI.UILabel();
            uiTextBox1 = new Sunny.UI.UITextBox();
            uiLabel1 = new Sunny.UI.UILabel();
            uiTabControl1 = new Sunny.UI.UITabControl();
            tabPage1 = new TabPage();
            uiRichTextBox1 = new Sunny.UI.UIRichTextBox();
            tabPage2 = new TabPage();
            uiGroupBox2 = new Sunny.UI.UIGroupBox();
            uiTextBox6 = new Sunny.UI.UITextBox();
            uiLabel6 = new Sunny.UI.UILabel();
            uiTextBox7 = new Sunny.UI.UITextBox();
            uiLabel7 = new Sunny.UI.UILabel();
            uiTextBox8 = new Sunny.UI.UITextBox();
            uiLabel8 = new Sunny.UI.UILabel();
            uiGroupBox5 = new Sunny.UI.UIGroupBox();
            uiButton6 = new Sunny.UI.UIButton();
            uiTextBox20 = new Sunny.UI.UITextBox();
            uiLabel19 = new Sunny.UI.UILabel();
            uiTextBox21 = new Sunny.UI.UITextBox();
            uiLabel20 = new Sunny.UI.UILabel();
            uiTextBox22 = new Sunny.UI.UITextBox();
            uiLabel21 = new Sunny.UI.UILabel();
            uiGroupBox4 = new Sunny.UI.UIGroupBox();
            uiButton5 = new Sunny.UI.UIButton();
            uiTextBox19 = new Sunny.UI.UITextBox();
            uiLabel18 = new Sunny.UI.UILabel();
            uiTextBox12 = new Sunny.UI.UITextBox();
            uiLabel11 = new Sunny.UI.UILabel();
            uiTextBox13 = new Sunny.UI.UITextBox();
            uiLabel12 = new Sunny.UI.UILabel();
            uiTextBox14 = new Sunny.UI.UITextBox();
            uiLabel13 = new Sunny.UI.UILabel();
            uiTextBox15 = new Sunny.UI.UITextBox();
            uiLabel14 = new Sunny.UI.UILabel();
            uiTextBox16 = new Sunny.UI.UITextBox();
            uiLabel15 = new Sunny.UI.UILabel();
            uiTextBox17 = new Sunny.UI.UITextBox();
            uiLabel16 = new Sunny.UI.UILabel();
            uiTextBox18 = new Sunny.UI.UITextBox();
            uiLabel17 = new Sunny.UI.UILabel();
            tabPage4 = new TabPage();
            uiDataGridView1 = new Sunny.UI.UIDataGridView();
            tabPage5 = new TabPage();
            uiGroupBox11 = new Sunny.UI.UIGroupBox();
            uiRadioButton2 = new Sunny.UI.UIRadioButton();
            uiRadioButton1 = new Sunny.UI.UIRadioButton();
            uiGroupBox10 = new Sunny.UI.UIGroupBox();
            uiCheckBox8 = new Sunny.UI.UICheckBox();
            uiCheckBox7 = new Sunny.UI.UICheckBox();
            uiCheckBox6 = new Sunny.UI.UICheckBox();
            uiCheckBox5 = new Sunny.UI.UICheckBox();
            uiCheckBox3 = new Sunny.UI.UICheckBox();
            uiButton11 = new Sunny.UI.UIButton();
            uiCheckBox4 = new Sunny.UI.UICheckBox();
            uiGroupBox9 = new Sunny.UI.UIGroupBox();
            uiLabel30 = new Sunny.UI.UILabel();
            uiComboBox4 = new Sunny.UI.UIComboBox();
            uiButton10 = new Sunny.UI.UIButton();
            uiGroupBox8 = new Sunny.UI.UIGroupBox();
            uiButton3 = new Sunny.UI.UIButton();
            uiButton2 = new Sunny.UI.UIButton();
            uiTextBox31 = new Sunny.UI.UITextBox();
            uiLabel34 = new Sunny.UI.UILabel();
            uiGroupBox7 = new Sunny.UI.UIGroupBox();
            uiLabel24 = new Sunny.UI.UILabel();
            uiComboBox2 = new Sunny.UI.UIComboBox();
            uiButton1 = new Sunny.UI.UIButton();
            uiButton9 = new Sunny.UI.UIButton();
            uiGroupBox6 = new Sunny.UI.UIGroupBox();
            uiComboBox5 = new Sunny.UI.UIComboBox();
            uiLabel37 = new Sunny.UI.UILabel();
            uiLabel36 = new Sunny.UI.UILabel();
            uiLabel35 = new Sunny.UI.UILabel();
            uiTextBox32 = new Sunny.UI.UITextBox();
            uiLabel33 = new Sunny.UI.UILabel();
            uiTextBox30 = new Sunny.UI.UITextBox();
            uiTextBox29 = new Sunny.UI.UITextBox();
            uiLabel32 = new Sunny.UI.UILabel();
            uiLabel31 = new Sunny.UI.UILabel();
            uiTextBox25 = new Sunny.UI.UITextBox();
            uiComboBox3 = new Sunny.UI.UIComboBox();
            uiTextBox5 = new Sunny.UI.UITextBox();
            uiLabel29 = new Sunny.UI.UILabel();
            uiButton7 = new Sunny.UI.UIButton();
            uiComboBox1 = new Sunny.UI.UIComboBox();
            uiLabel28 = new Sunny.UI.UILabel();
            uiTextBox28 = new Sunny.UI.UITextBox();
            uiLabel27 = new Sunny.UI.UILabel();
            uiTextBox27 = new Sunny.UI.UITextBox();
            uiLabel26 = new Sunny.UI.UILabel();
            uiTextBox26 = new Sunny.UI.UITextBox();
            uiLabel25 = new Sunny.UI.UILabel();
            uiTextBox24 = new Sunny.UI.UITextBox();
            uiLabel23 = new Sunny.UI.UILabel();
            uiTextBox23 = new Sunny.UI.UITextBox();
            uiLabel22 = new Sunny.UI.UILabel();
            uiGroupBox3 = new Sunny.UI.UIGroupBox();
            uiCheckBox2 = new Sunny.UI.UICheckBox();
            uiButton8 = new Sunny.UI.UIButton();
            uiCheckBox1 = new Sunny.UI.UICheckBox();
            uiCheckBox9 = new Sunny.UI.UICheckBox();
            uiGroupBox1.SuspendLayout();
            uiTabControl1.SuspendLayout();
            tabPage1.SuspendLayout();
            tabPage2.SuspendLayout();
            uiGroupBox2.SuspendLayout();
            uiGroupBox5.SuspendLayout();
            uiGroupBox4.SuspendLayout();
            tabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)uiDataGridView1).BeginInit();
            tabPage5.SuspendLayout();
            uiGroupBox11.SuspendLayout();
            uiGroupBox10.SuspendLayout();
            uiGroupBox9.SuspendLayout();
            uiGroupBox8.SuspendLayout();
            uiGroupBox7.SuspendLayout();
            uiGroupBox6.SuspendLayout();
            uiGroupBox3.SuspendLayout();
            SuspendLayout();
            // 
            // uiGroupBox1
            // 
            uiGroupBox1.Controls.Add(uiButton4);
            uiGroupBox1.Controls.Add(uiTextBox11);
            uiGroupBox1.Controls.Add(uiLabel10);
            uiGroupBox1.Controls.Add(uiTextBox10);
            uiGroupBox1.Controls.Add(uiLabel9);
            uiGroupBox1.Controls.Add(uiTextBox9);
            uiGroupBox1.Controls.Add(uiLabel5);
            uiGroupBox1.Controls.Add(uiTextBox4);
            uiGroupBox1.Controls.Add(uiLabel4);
            uiGroupBox1.Controls.Add(uiTextBox3);
            uiGroupBox1.Controls.Add(uiLabel3);
            uiGroupBox1.Controls.Add(uiTextBox2);
            uiGroupBox1.Controls.Add(uiLabel2);
            uiGroupBox1.Controls.Add(uiTextBox1);
            uiGroupBox1.Controls.Add(uiLabel1);
            uiGroupBox1.FillColor = Color.FromArgb(238, 251, 250);
            uiGroupBox1.FillColor2 = Color.FromArgb(238, 251, 250);
            uiGroupBox1.Font = new Font("宋体", 14F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox1.Location = new Point(13, 16);
            uiGroupBox1.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox1.MinimumSize = new Size(1, 1);
            uiGroupBox1.Name = "uiGroupBox1";
            uiGroupBox1.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox1.RectColor = Color.FromArgb(0, 190, 172);
            uiGroupBox1.Size = new Size(356, 504);
            uiGroupBox1.Style = Sunny.UI.UIStyle.Custom;
            uiGroupBox1.TabIndex = 0;
            uiGroupBox1.Text = "外观前高度检测";
            uiGroupBox1.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // uiButton4
            // 
            uiButton4.FillColor = Color.FromArgb(0, 190, 172);
            uiButton4.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton4.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton4.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton4.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton4.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton4.LightColor = Color.FromArgb(238, 251, 250);
            uiButton4.Location = new Point(208, 444);
            uiButton4.MinimumSize = new Size(1, 1);
            uiButton4.Name = "uiButton4";
            uiButton4.RectColor = Color.FromArgb(0, 190, 172);
            uiButton4.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton4.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton4.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton4.Size = new Size(100, 35);
            uiButton4.Style = Sunny.UI.UIStyle.Custom;
            uiButton4.TabIndex = 8;
            uiButton4.Text = "读取";
            uiButton4.Click += uiButton4_Click;
            // 
            // uiTextBox11
            // 
            uiTextBox11.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox11.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox11.Location = new Point(120, 336);
            uiTextBox11.Margin = new Padding(4, 5, 4, 5);
            uiTextBox11.MinimumSize = new Size(1, 16);
            uiTextBox11.Name = "uiTextBox11";
            uiTextBox11.Padding = new Padding(5);
            uiTextBox11.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox11.ShowText = false;
            uiTextBox11.Size = new Size(188, 36);
            uiTextBox11.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox11.TabIndex = 7;
            uiTextBox11.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox11.Watermark = "";
            // 
            // uiLabel10
            // 
            uiLabel10.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel10.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel10.Location = new Point(32, 345);
            uiLabel10.Name = "uiLabel10";
            uiLabel10.Size = new Size(81, 29);
            uiLabel10.TabIndex = 6;
            uiLabel10.Text = "高度6";
            // 
            // uiTextBox10
            // 
            uiTextBox10.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox10.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox10.Location = new Point(120, 290);
            uiTextBox10.Margin = new Padding(4, 5, 4, 5);
            uiTextBox10.MinimumSize = new Size(1, 16);
            uiTextBox10.Name = "uiTextBox10";
            uiTextBox10.Padding = new Padding(5);
            uiTextBox10.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox10.ShowText = false;
            uiTextBox10.Size = new Size(188, 36);
            uiTextBox10.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox10.TabIndex = 7;
            uiTextBox10.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox10.Watermark = "";
            // 
            // uiLabel9
            // 
            uiLabel9.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel9.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel9.Location = new Point(32, 299);
            uiLabel9.Name = "uiLabel9";
            uiLabel9.Size = new Size(81, 29);
            uiLabel9.TabIndex = 6;
            uiLabel9.Text = "高度5";
            // 
            // uiTextBox9
            // 
            uiTextBox9.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox9.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox9.Location = new Point(120, 244);
            uiTextBox9.Margin = new Padding(4, 5, 4, 5);
            uiTextBox9.MinimumSize = new Size(1, 16);
            uiTextBox9.Name = "uiTextBox9";
            uiTextBox9.Padding = new Padding(5);
            uiTextBox9.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox9.ShowText = false;
            uiTextBox9.Size = new Size(188, 36);
            uiTextBox9.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox9.TabIndex = 7;
            uiTextBox9.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox9.Watermark = "";
            // 
            // uiLabel5
            // 
            uiLabel5.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel5.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel5.Location = new Point(32, 253);
            uiLabel5.Name = "uiLabel5";
            uiLabel5.Size = new Size(81, 29);
            uiLabel5.TabIndex = 6;
            uiLabel5.Text = "高度4";
            // 
            // uiTextBox4
            // 
            uiTextBox4.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox4.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox4.Location = new Point(120, 198);
            uiTextBox4.Margin = new Padding(4, 5, 4, 5);
            uiTextBox4.MinimumSize = new Size(1, 16);
            uiTextBox4.Name = "uiTextBox4";
            uiTextBox4.Padding = new Padding(5);
            uiTextBox4.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox4.ShowText = false;
            uiTextBox4.Size = new Size(188, 36);
            uiTextBox4.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox4.TabIndex = 5;
            uiTextBox4.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox4.Watermark = "";
            // 
            // uiLabel4
            // 
            uiLabel4.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel4.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel4.Location = new Point(32, 207);
            uiLabel4.Name = "uiLabel4";
            uiLabel4.Size = new Size(81, 29);
            uiLabel4.TabIndex = 4;
            uiLabel4.Text = "高度3";
            // 
            // uiTextBox3
            // 
            uiTextBox3.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox3.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox3.Location = new Point(120, 152);
            uiTextBox3.Margin = new Padding(4, 5, 4, 5);
            uiTextBox3.MinimumSize = new Size(1, 16);
            uiTextBox3.Name = "uiTextBox3";
            uiTextBox3.Padding = new Padding(5);
            uiTextBox3.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox3.ShowText = false;
            uiTextBox3.Size = new Size(188, 36);
            uiTextBox3.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox3.TabIndex = 5;
            uiTextBox3.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox3.Watermark = "";
            // 
            // uiLabel3
            // 
            uiLabel3.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel3.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel3.Location = new Point(32, 161);
            uiLabel3.Name = "uiLabel3";
            uiLabel3.Size = new Size(81, 29);
            uiLabel3.TabIndex = 4;
            uiLabel3.Text = "高度2";
            // 
            // uiTextBox2
            // 
            uiTextBox2.BackColor = Color.FromArgb(238, 251, 250);
            uiTextBox2.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox2.Location = new Point(120, 106);
            uiTextBox2.Margin = new Padding(4, 5, 4, 5);
            uiTextBox2.MinimumSize = new Size(1, 16);
            uiTextBox2.Name = "uiTextBox2";
            uiTextBox2.Padding = new Padding(5);
            uiTextBox2.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox2.ShowText = false;
            uiTextBox2.Size = new Size(188, 36);
            uiTextBox2.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox2.TabIndex = 3;
            uiTextBox2.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox2.Watermark = "";
            // 
            // uiLabel2
            // 
            uiLabel2.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel2.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel2.Location = new Point(32, 115);
            uiLabel2.Name = "uiLabel2";
            uiLabel2.Size = new Size(81, 29);
            uiLabel2.TabIndex = 2;
            uiLabel2.Text = "高度1";
            // 
            // uiTextBox1
            // 
            uiTextBox1.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox1.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox1.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox1.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox1.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox1.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox1.ButtonStyleInherited = false;
            uiTextBox1.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox1.Location = new Point(120, 44);
            uiTextBox1.Margin = new Padding(4, 5, 4, 5);
            uiTextBox1.MinimumSize = new Size(1, 16);
            uiTextBox1.Name = "uiTextBox1";
            uiTextBox1.Padding = new Padding(5);
            uiTextBox1.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox1.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox1.ScrollBarStyleInherited = false;
            uiTextBox1.ShowText = false;
            uiTextBox1.Size = new Size(188, 36);
            uiTextBox1.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox1.TabIndex = 1;
            uiTextBox1.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox1.Watermark = "";
            // 
            // uiLabel1
            // 
            uiLabel1.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel1.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel1.Location = new Point(32, 51);
            uiLabel1.Name = "uiLabel1";
            uiLabel1.Size = new Size(92, 29);
            uiLabel1.TabIndex = 0;
            uiLabel1.Text = "编号:";
            // 
            // uiTabControl1
            // 
            uiTabControl1.Controls.Add(tabPage1);
            uiTabControl1.Controls.Add(tabPage2);
            uiTabControl1.Controls.Add(tabPage4);
            uiTabControl1.Controls.Add(tabPage5);
            uiTabControl1.DrawMode = TabDrawMode.OwnerDrawFixed;
            uiTabControl1.FillColor = Color.FromArgb(238, 251, 250);
            uiTabControl1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTabControl1.ItemSize = new Size(150, 40);
            uiTabControl1.Location = new Point(0, 0);
            uiTabControl1.MainPage = "";
            uiTabControl1.Name = "uiTabControl1";
            uiTabControl1.SelectedIndex = 0;
            uiTabControl1.Size = new Size(1670, 849);
            uiTabControl1.SizeMode = TabSizeMode.Fixed;
            uiTabControl1.Style = Sunny.UI.UIStyle.Custom;
            uiTabControl1.TabIndex = 1;
            uiTabControl1.TabSelectedForeColor = Color.FromArgb(0, 190, 172);
            uiTabControl1.TabSelectedHighColor = Color.FromArgb(0, 190, 172);
            uiTabControl1.TabUnSelectedForeColor = Color.FromArgb(240, 240, 240);
            uiTabControl1.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point);
            // 
            // tabPage1
            // 
            tabPage1.BackColor = Color.FromArgb(238, 251, 250);
            tabPage1.Controls.Add(uiRichTextBox1);
            tabPage1.Location = new Point(0, 40);
            tabPage1.Name = "tabPage1";
            tabPage1.Size = new Size(1670, 809);
            tabPage1.TabIndex = 0;
            tabPage1.Text = "报警";
            // 
            // uiRichTextBox1
            // 
            uiRichTextBox1.FillColor = Color.FromArgb(238, 251, 250);
            uiRichTextBox1.FillColor2 = Color.FromArgb(238, 251, 250);
            uiRichTextBox1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiRichTextBox1.Location = new Point(0, 0);
            uiRichTextBox1.Margin = new Padding(4, 5, 4, 5);
            uiRichTextBox1.MinimumSize = new Size(1, 1);
            uiRichTextBox1.Name = "uiRichTextBox1";
            uiRichTextBox1.Padding = new Padding(2);
            uiRichTextBox1.RectColor = Color.FromArgb(0, 190, 172);
            uiRichTextBox1.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiRichTextBox1.ScrollBarStyleInherited = false;
            uiRichTextBox1.ShowText = false;
            uiRichTextBox1.Size = new Size(1670, 789);
            uiRichTextBox1.Style = Sunny.UI.UIStyle.Custom;
            uiRichTextBox1.TabIndex = 0;
            uiRichTextBox1.TextAlignment = ContentAlignment.MiddleCenter;
            // 
            // tabPage2
            // 
            tabPage2.BackColor = Color.FromArgb(238, 251, 250);
            tabPage2.Controls.Add(uiGroupBox2);
            tabPage2.Controls.Add(uiGroupBox5);
            tabPage2.Controls.Add(uiGroupBox4);
            tabPage2.Controls.Add(uiGroupBox1);
            tabPage2.Location = new Point(0, 40);
            tabPage2.Name = "tabPage2";
            tabPage2.Size = new Size(200, 60);
            tabPage2.TabIndex = 1;
            tabPage2.Text = "实时数据";
            // 
            // uiGroupBox2
            // 
            uiGroupBox2.Controls.Add(uiTextBox6);
            uiGroupBox2.Controls.Add(uiLabel6);
            uiGroupBox2.Controls.Add(uiTextBox7);
            uiGroupBox2.Controls.Add(uiLabel7);
            uiGroupBox2.Controls.Add(uiTextBox8);
            uiGroupBox2.Controls.Add(uiLabel8);
            uiGroupBox2.FillColor = Color.FromArgb(238, 251, 250);
            uiGroupBox2.FillColor2 = Color.FromArgb(238, 251, 250);
            uiGroupBox2.Font = new Font("宋体", 14F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox2.Location = new Point(1259, 16);
            uiGroupBox2.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox2.MinimumSize = new Size(1, 1);
            uiGroupBox2.Name = "uiGroupBox2";
            uiGroupBox2.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox2.RectColor = Color.FromArgb(0, 190, 172);
            uiGroupBox2.Size = new Size(356, 504);
            uiGroupBox2.Style = Sunny.UI.UIStyle.Custom;
            uiGroupBox2.TabIndex = 11;
            uiGroupBox2.Text = "耐压检测";
            uiGroupBox2.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // uiTextBox6
            // 
            uiTextBox6.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox6.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox6.Location = new Point(120, 152);
            uiTextBox6.Margin = new Padding(4, 5, 4, 5);
            uiTextBox6.MinimumSize = new Size(1, 16);
            uiTextBox6.Name = "uiTextBox6";
            uiTextBox6.Padding = new Padding(5);
            uiTextBox6.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox6.ShowText = false;
            uiTextBox6.Size = new Size(188, 36);
            uiTextBox6.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox6.TabIndex = 5;
            uiTextBox6.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox6.Watermark = "";
            // 
            // uiLabel6
            // 
            uiLabel6.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel6.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel6.Location = new Point(49, 161);
            uiLabel6.Name = "uiLabel6";
            uiLabel6.Size = new Size(61, 29);
            uiLabel6.TabIndex = 4;
            uiLabel6.Text = "结论";
            // 
            // uiTextBox7
            // 
            uiTextBox7.BackColor = Color.FromArgb(238, 251, 250);
            uiTextBox7.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox7.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox7.Location = new Point(120, 106);
            uiTextBox7.Margin = new Padding(4, 5, 4, 5);
            uiTextBox7.MinimumSize = new Size(1, 16);
            uiTextBox7.Name = "uiTextBox7";
            uiTextBox7.Padding = new Padding(5);
            uiTextBox7.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox7.ShowText = false;
            uiTextBox7.Size = new Size(188, 36);
            uiTextBox7.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox7.TabIndex = 3;
            uiTextBox7.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox7.Watermark = "";
            // 
            // uiLabel7
            // 
            uiLabel7.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel7.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel7.Location = new Point(14, 115);
            uiLabel7.Name = "uiLabel7";
            uiLabel7.Size = new Size(92, 29);
            uiLabel7.TabIndex = 2;
            uiLabel7.Text = "耐压数据";
            // 
            // uiTextBox8
            // 
            uiTextBox8.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox8.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox8.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox8.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox8.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox8.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox8.ButtonStyleInherited = false;
            uiTextBox8.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox8.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox8.Location = new Point(120, 44);
            uiTextBox8.Margin = new Padding(4, 5, 4, 5);
            uiTextBox8.MinimumSize = new Size(1, 16);
            uiTextBox8.Name = "uiTextBox8";
            uiTextBox8.Padding = new Padding(5);
            uiTextBox8.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox8.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox8.ScrollBarStyleInherited = false;
            uiTextBox8.ShowText = false;
            uiTextBox8.Size = new Size(188, 36);
            uiTextBox8.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox8.TabIndex = 1;
            uiTextBox8.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox8.Watermark = "";
            // 
            // uiLabel8
            // 
            uiLabel8.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel8.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel8.Location = new Point(45, 51);
            uiLabel8.Name = "uiLabel8";
            uiLabel8.Size = new Size(72, 29);
            uiLabel8.TabIndex = 0;
            uiLabel8.Text = "编号:";
            // 
            // uiGroupBox5
            // 
            uiGroupBox5.Controls.Add(uiButton6);
            uiGroupBox5.Controls.Add(uiTextBox20);
            uiGroupBox5.Controls.Add(uiLabel19);
            uiGroupBox5.Controls.Add(uiTextBox21);
            uiGroupBox5.Controls.Add(uiLabel20);
            uiGroupBox5.Controls.Add(uiTextBox22);
            uiGroupBox5.Controls.Add(uiLabel21);
            uiGroupBox5.FillColor = Color.FromArgb(238, 251, 250);
            uiGroupBox5.FillColor2 = Color.FromArgb(238, 251, 250);
            uiGroupBox5.Font = new Font("宋体", 14F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox5.Location = new Point(765, 16);
            uiGroupBox5.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox5.MinimumSize = new Size(1, 1);
            uiGroupBox5.Name = "uiGroupBox5";
            uiGroupBox5.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox5.RectColor = Color.FromArgb(0, 190, 172);
            uiGroupBox5.Size = new Size(468, 504);
            uiGroupBox5.Style = Sunny.UI.UIStyle.Custom;
            uiGroupBox5.TabIndex = 9;
            uiGroupBox5.Text = "二维码检测";
            uiGroupBox5.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // uiButton6
            // 
            uiButton6.FillColor = Color.FromArgb(0, 190, 172);
            uiButton6.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton6.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton6.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton6.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton6.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton6.LightColor = Color.FromArgb(238, 251, 250);
            uiButton6.Location = new Point(331, 444);
            uiButton6.MinimumSize = new Size(1, 1);
            uiButton6.Name = "uiButton6";
            uiButton6.RectColor = Color.FromArgb(0, 190, 172);
            uiButton6.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton6.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton6.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton6.Size = new Size(100, 35);
            uiButton6.Style = Sunny.UI.UIStyle.Custom;
            uiButton6.TabIndex = 10;
            uiButton6.Text = "读取";
            uiButton6.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton6.Click += uiButton6_Click;
            // 
            // uiTextBox20
            // 
            uiTextBox20.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox20.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox20.Location = new Point(120, 152);
            uiTextBox20.Margin = new Padding(4, 5, 4, 5);
            uiTextBox20.MinimumSize = new Size(1, 16);
            uiTextBox20.Name = "uiTextBox20";
            uiTextBox20.Padding = new Padding(5);
            uiTextBox20.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox20.ShowText = false;
            uiTextBox20.Size = new Size(311, 36);
            uiTextBox20.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox20.TabIndex = 5;
            uiTextBox20.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox20.Watermark = "";
            // 
            // uiLabel19
            // 
            uiLabel19.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel19.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel19.Location = new Point(49, 161);
            uiLabel19.Name = "uiLabel19";
            uiLabel19.Size = new Size(61, 29);
            uiLabel19.TabIndex = 4;
            uiLabel19.Text = "等级";
            // 
            // uiTextBox21
            // 
            uiTextBox21.BackColor = Color.FromArgb(238, 251, 250);
            uiTextBox21.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox21.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox21.Location = new Point(120, 106);
            uiTextBox21.Margin = new Padding(4, 5, 4, 5);
            uiTextBox21.MinimumSize = new Size(1, 16);
            uiTextBox21.Name = "uiTextBox21";
            uiTextBox21.Padding = new Padding(5);
            uiTextBox21.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox21.ShowText = false;
            uiTextBox21.Size = new Size(311, 36);
            uiTextBox21.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox21.TabIndex = 3;
            uiTextBox21.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox21.Watermark = "";
            // 
            // uiLabel20
            // 
            uiLabel20.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel20.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel20.Location = new Point(25, 115);
            uiLabel20.Name = "uiLabel20";
            uiLabel20.Size = new Size(92, 29);
            uiLabel20.TabIndex = 2;
            uiLabel20.Text = "二维码";
            // 
            // uiTextBox22
            // 
            uiTextBox22.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox22.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox22.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox22.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox22.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox22.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox22.ButtonStyleInherited = false;
            uiTextBox22.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox22.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox22.Location = new Point(120, 44);
            uiTextBox22.Margin = new Padding(4, 5, 4, 5);
            uiTextBox22.MinimumSize = new Size(1, 16);
            uiTextBox22.Name = "uiTextBox22";
            uiTextBox22.Padding = new Padding(5);
            uiTextBox22.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox22.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox22.ScrollBarStyleInherited = false;
            uiTextBox22.ShowText = false;
            uiTextBox22.Size = new Size(311, 36);
            uiTextBox22.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox22.TabIndex = 1;
            uiTextBox22.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox22.Watermark = "";
            // 
            // uiLabel21
            // 
            uiLabel21.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel21.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel21.Location = new Point(45, 51);
            uiLabel21.Name = "uiLabel21";
            uiLabel21.Size = new Size(72, 29);
            uiLabel21.TabIndex = 0;
            uiLabel21.Text = "编号:";
            // 
            // uiGroupBox4
            // 
            uiGroupBox4.Controls.Add(uiButton5);
            uiGroupBox4.Controls.Add(uiTextBox19);
            uiGroupBox4.Controls.Add(uiLabel18);
            uiGroupBox4.Controls.Add(uiTextBox12);
            uiGroupBox4.Controls.Add(uiLabel11);
            uiGroupBox4.Controls.Add(uiTextBox13);
            uiGroupBox4.Controls.Add(uiLabel12);
            uiGroupBox4.Controls.Add(uiTextBox14);
            uiGroupBox4.Controls.Add(uiLabel13);
            uiGroupBox4.Controls.Add(uiTextBox15);
            uiGroupBox4.Controls.Add(uiLabel14);
            uiGroupBox4.Controls.Add(uiTextBox16);
            uiGroupBox4.Controls.Add(uiLabel15);
            uiGroupBox4.Controls.Add(uiTextBox17);
            uiGroupBox4.Controls.Add(uiLabel16);
            uiGroupBox4.Controls.Add(uiTextBox18);
            uiGroupBox4.Controls.Add(uiLabel17);
            uiGroupBox4.FillColor = Color.FromArgb(238, 251, 250);
            uiGroupBox4.FillColor2 = Color.FromArgb(238, 251, 250);
            uiGroupBox4.Font = new Font("宋体", 14F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox4.Location = new Point(389, 16);
            uiGroupBox4.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox4.MinimumSize = new Size(1, 1);
            uiGroupBox4.Name = "uiGroupBox4";
            uiGroupBox4.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox4.RectColor = Color.FromArgb(0, 190, 172);
            uiGroupBox4.Size = new Size(356, 504);
            uiGroupBox4.Style = Sunny.UI.UIStyle.Custom;
            uiGroupBox4.TabIndex = 8;
            uiGroupBox4.Text = "高度检测";
            uiGroupBox4.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // uiButton5
            // 
            uiButton5.FillColor = Color.FromArgb(0, 190, 172);
            uiButton5.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton5.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton5.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton5.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton5.LightColor = Color.FromArgb(238, 251, 250);
            uiButton5.Location = new Point(208, 444);
            uiButton5.MinimumSize = new Size(1, 1);
            uiButton5.Name = "uiButton5";
            uiButton5.RectColor = Color.FromArgb(0, 190, 172);
            uiButton5.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton5.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton5.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton5.Size = new Size(100, 35);
            uiButton5.Style = Sunny.UI.UIStyle.Custom;
            uiButton5.TabIndex = 9;
            uiButton5.Text = "读取";
            uiButton5.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton5.Click += uiButton5_Click;
            // 
            // uiTextBox19
            // 
            uiTextBox19.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox19.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox19.Location = new Point(120, 382);
            uiTextBox19.Margin = new Padding(4, 5, 4, 5);
            uiTextBox19.MinimumSize = new Size(1, 16);
            uiTextBox19.Name = "uiTextBox19";
            uiTextBox19.Padding = new Padding(5);
            uiTextBox19.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox19.ShowText = false;
            uiTextBox19.Size = new Size(188, 36);
            uiTextBox19.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox19.TabIndex = 9;
            uiTextBox19.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox19.Watermark = "";
            // 
            // uiLabel18
            // 
            uiLabel18.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel18.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel18.Location = new Point(32, 391);
            uiLabel18.Name = "uiLabel18";
            uiLabel18.Size = new Size(92, 29);
            uiLabel18.TabIndex = 8;
            uiLabel18.Text = "250高度4";
            // 
            // uiTextBox12
            // 
            uiTextBox12.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox12.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox12.Location = new Point(120, 336);
            uiTextBox12.Margin = new Padding(4, 5, 4, 5);
            uiTextBox12.MinimumSize = new Size(1, 16);
            uiTextBox12.Name = "uiTextBox12";
            uiTextBox12.Padding = new Padding(5);
            uiTextBox12.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox12.ShowText = false;
            uiTextBox12.Size = new Size(188, 36);
            uiTextBox12.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox12.TabIndex = 7;
            uiTextBox12.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox12.Watermark = "";
            // 
            // uiLabel11
            // 
            uiLabel11.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel11.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel11.Location = new Point(32, 345);
            uiLabel11.Name = "uiLabel11";
            uiLabel11.Size = new Size(92, 29);
            uiLabel11.TabIndex = 6;
            uiLabel11.Text = "250高度3";
            // 
            // uiTextBox13
            // 
            uiTextBox13.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox13.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox13.Location = new Point(120, 290);
            uiTextBox13.Margin = new Padding(4, 5, 4, 5);
            uiTextBox13.MinimumSize = new Size(1, 16);
            uiTextBox13.Name = "uiTextBox13";
            uiTextBox13.Padding = new Padding(5);
            uiTextBox13.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox13.ShowText = false;
            uiTextBox13.Size = new Size(188, 36);
            uiTextBox13.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox13.TabIndex = 7;
            uiTextBox13.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox13.Watermark = "";
            // 
            // uiLabel12
            // 
            uiLabel12.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel12.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel12.Location = new Point(32, 299);
            uiLabel12.Name = "uiLabel12";
            uiLabel12.Size = new Size(92, 29);
            uiLabel12.TabIndex = 6;
            uiLabel12.Text = "250高度2";
            // 
            // uiTextBox14
            // 
            uiTextBox14.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox14.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox14.Location = new Point(120, 244);
            uiTextBox14.Margin = new Padding(4, 5, 4, 5);
            uiTextBox14.MinimumSize = new Size(1, 16);
            uiTextBox14.Name = "uiTextBox14";
            uiTextBox14.Padding = new Padding(5);
            uiTextBox14.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox14.ShowText = false;
            uiTextBox14.Size = new Size(188, 36);
            uiTextBox14.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox14.TabIndex = 7;
            uiTextBox14.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox14.Watermark = "";
            // 
            // uiLabel13
            // 
            uiLabel13.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel13.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel13.Location = new Point(32, 253);
            uiLabel13.Name = "uiLabel13";
            uiLabel13.Size = new Size(92, 29);
            uiLabel13.TabIndex = 6;
            uiLabel13.Text = "250高度1";
            // 
            // uiTextBox15
            // 
            uiTextBox15.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox15.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox15.Location = new Point(120, 198);
            uiTextBox15.Margin = new Padding(4, 5, 4, 5);
            uiTextBox15.MinimumSize = new Size(1, 16);
            uiTextBox15.Name = "uiTextBox15";
            uiTextBox15.Padding = new Padding(5);
            uiTextBox15.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox15.ShowText = false;
            uiTextBox15.Size = new Size(188, 36);
            uiTextBox15.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox15.TabIndex = 5;
            uiTextBox15.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox15.Watermark = "";
            // 
            // uiLabel14
            // 
            uiLabel14.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel14.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel14.Location = new Point(32, 207);
            uiLabel14.Name = "uiLabel14";
            uiLabel14.Size = new Size(92, 29);
            uiLabel14.TabIndex = 4;
            uiLabel14.Text = "150高度3";
            // 
            // uiTextBox16
            // 
            uiTextBox16.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox16.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox16.Location = new Point(120, 152);
            uiTextBox16.Margin = new Padding(4, 5, 4, 5);
            uiTextBox16.MinimumSize = new Size(1, 16);
            uiTextBox16.Name = "uiTextBox16";
            uiTextBox16.Padding = new Padding(5);
            uiTextBox16.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox16.ShowText = false;
            uiTextBox16.Size = new Size(188, 36);
            uiTextBox16.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox16.TabIndex = 5;
            uiTextBox16.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox16.Watermark = "";
            // 
            // uiLabel15
            // 
            uiLabel15.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel15.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel15.Location = new Point(32, 161);
            uiLabel15.Name = "uiLabel15";
            uiLabel15.Size = new Size(92, 29);
            uiLabel15.TabIndex = 4;
            uiLabel15.Text = "150高度2";
            // 
            // uiTextBox17
            // 
            uiTextBox17.BackColor = Color.FromArgb(238, 251, 250);
            uiTextBox17.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox17.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox17.Location = new Point(120, 106);
            uiTextBox17.Margin = new Padding(4, 5, 4, 5);
            uiTextBox17.MinimumSize = new Size(1, 16);
            uiTextBox17.Name = "uiTextBox17";
            uiTextBox17.Padding = new Padding(5);
            uiTextBox17.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox17.ShowText = false;
            uiTextBox17.Size = new Size(188, 36);
            uiTextBox17.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox17.TabIndex = 3;
            uiTextBox17.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox17.Watermark = "";
            // 
            // uiLabel16
            // 
            uiLabel16.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel16.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel16.Location = new Point(32, 115);
            uiLabel16.Name = "uiLabel16";
            uiLabel16.Size = new Size(92, 29);
            uiLabel16.TabIndex = 2;
            uiLabel16.Text = "150高度1";
            // 
            // uiTextBox18
            // 
            uiTextBox18.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox18.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox18.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox18.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox18.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox18.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox18.ButtonStyleInherited = false;
            uiTextBox18.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox18.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox18.Location = new Point(120, 44);
            uiTextBox18.Margin = new Padding(4, 5, 4, 5);
            uiTextBox18.MinimumSize = new Size(1, 16);
            uiTextBox18.Name = "uiTextBox18";
            uiTextBox18.Padding = new Padding(5);
            uiTextBox18.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox18.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox18.ScrollBarStyleInherited = false;
            uiTextBox18.ShowText = false;
            uiTextBox18.Size = new Size(188, 36);
            uiTextBox18.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox18.TabIndex = 1;
            uiTextBox18.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox18.Watermark = "";
            // 
            // uiLabel17
            // 
            uiLabel17.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel17.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel17.Location = new Point(32, 51);
            uiLabel17.Name = "uiLabel17";
            uiLabel17.Size = new Size(92, 29);
            uiLabel17.TabIndex = 0;
            uiLabel17.Text = "编号:";
            // 
            // tabPage4
            // 
            tabPage4.BackColor = Color.FromArgb(238, 251, 250);
            tabPage4.Controls.Add(uiDataGridView1);
            tabPage4.Location = new Point(0, 40);
            tabPage4.Name = "tabPage4";
            tabPage4.Size = new Size(200, 60);
            tabPage4.TabIndex = 3;
            tabPage4.Text = "数据展示";
            // 
            // uiDataGridView1
            // 
            dataGridViewCellStyle1.BackColor = Color.FromArgb(238, 251, 250);
            uiDataGridView1.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            uiDataGridView1.BackgroundColor = Color.FromArgb(238, 251, 250);
            uiDataGridView1.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = Color.FromArgb(0, 190, 172);
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(0, 190, 172);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            uiDataGridView1.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            uiDataGridView1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = Color.White;
            dataGridViewCellStyle3.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle3.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle3.SelectionBackColor = Color.FromArgb(204, 242, 238);
            dataGridViewCellStyle3.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle3.WrapMode = DataGridViewTriState.False;
            uiDataGridView1.DefaultCellStyle = dataGridViewCellStyle3;
            uiDataGridView1.EnableHeadersVisualStyles = false;
            uiDataGridView1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiDataGridView1.GridColor = Color.FromArgb(34, 199, 183);
            uiDataGridView1.Location = new Point(0, 1);
            uiDataGridView1.Name = "uiDataGridView1";
            uiDataGridView1.RectColor = Color.FromArgb(0, 190, 172);
            dataGridViewCellStyle4.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle4.BackColor = Color.FromArgb(238, 251, 250);
            dataGridViewCellStyle4.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle4.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle4.SelectionBackColor = Color.FromArgb(0, 190, 172);
            dataGridViewCellStyle4.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle4.WrapMode = DataGridViewTriState.True;
            uiDataGridView1.RowHeadersDefaultCellStyle = dataGridViewCellStyle4;
            uiDataGridView1.RowHeadersWidth = 51;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(204, 242, 238);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            uiDataGridView1.RowsDefaultCellStyle = dataGridViewCellStyle5;
            uiDataGridView1.RowTemplate.Height = 29;
            uiDataGridView1.ScrollBarBackColor = Color.FromArgb(238, 251, 250);
            uiDataGridView1.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiDataGridView1.ScrollBarRectColor = Color.FromArgb(0, 190, 172);
            uiDataGridView1.ScrollBarStyleInherited = false;
            uiDataGridView1.SelectedIndex = -1;
            uiDataGridView1.Size = new Size(1670, 789);
            uiDataGridView1.StripeOddColor = Color.FromArgb(238, 251, 250);
            uiDataGridView1.Style = Sunny.UI.UIStyle.Custom;
            uiDataGridView1.TabIndex = 0;
            // 
            // tabPage5
            // 
            tabPage5.BackColor = Color.FromArgb(238, 251, 250);
            tabPage5.Controls.Add(uiGroupBox11);
            tabPage5.Controls.Add(uiGroupBox10);
            tabPage5.Controls.Add(uiGroupBox9);
            tabPage5.Controls.Add(uiGroupBox8);
            tabPage5.Controls.Add(uiGroupBox7);
            tabPage5.Controls.Add(uiGroupBox6);
            tabPage5.Controls.Add(uiGroupBox3);
            tabPage5.Location = new Point(0, 40);
            tabPage5.Name = "tabPage5";
            tabPage5.Size = new Size(1670, 809);
            tabPage5.TabIndex = 4;
            tabPage5.Text = "系统设置";
            // 
            // uiGroupBox11
            // 
            uiGroupBox11.Controls.Add(uiRadioButton2);
            uiGroupBox11.Controls.Add(uiRadioButton1);
            uiGroupBox11.FillColor = Color.FromArgb(238, 251, 250);
            uiGroupBox11.FillColor2 = Color.FromArgb(238, 251, 250);
            uiGroupBox11.Font = new Font("宋体", 14F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox11.Location = new Point(1223, 410);
            uiGroupBox11.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox11.MinimumSize = new Size(1, 1);
            uiGroupBox11.Name = "uiGroupBox11";
            uiGroupBox11.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox11.RectColor = Color.FromArgb(0, 190, 172);
            uiGroupBox11.Size = new Size(348, 340);
            uiGroupBox11.Style = Sunny.UI.UIStyle.Custom;
            uiGroupBox11.TabIndex = 4;
            uiGroupBox11.Text = "耐压仪试验项配置";
            uiGroupBox11.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // uiRadioButton2
            // 
            uiRadioButton2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiRadioButton2.Location = new Point(26, 88);
            uiRadioButton2.MinimumSize = new Size(1, 1);
            uiRadioButton2.Name = "uiRadioButton2";
            uiRadioButton2.RadioButtonColor = Color.FromArgb(0, 190, 172);
            uiRadioButton2.Size = new Size(150, 29);
            uiRadioButton2.Style = Sunny.UI.UIStyle.Custom;
            uiRadioButton2.TabIndex = 1;
            uiRadioButton2.Text = "直流电流";
            // 
            // uiRadioButton1
            // 
            uiRadioButton1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiRadioButton1.Location = new Point(26, 53);
            uiRadioButton1.MinimumSize = new Size(1, 1);
            uiRadioButton1.Name = "uiRadioButton1";
            uiRadioButton1.RadioButtonColor = Color.FromArgb(0, 190, 172);
            uiRadioButton1.Size = new Size(150, 29);
            uiRadioButton1.Style = Sunny.UI.UIStyle.Custom;
            uiRadioButton1.TabIndex = 0;
            uiRadioButton1.Text = "绝缘电阻";
            // 
            // uiGroupBox10
            // 
            uiGroupBox10.Controls.Add(uiCheckBox8);
            uiGroupBox10.Controls.Add(uiCheckBox7);
            uiGroupBox10.Controls.Add(uiCheckBox6);
            uiGroupBox10.Controls.Add(uiCheckBox5);
            uiGroupBox10.Controls.Add(uiCheckBox3);
            uiGroupBox10.Controls.Add(uiButton11);
            uiGroupBox10.Controls.Add(uiCheckBox4);
            uiGroupBox10.FillColor = Color.FromArgb(238, 251, 250);
            uiGroupBox10.FillColor2 = Color.FromArgb(238, 251, 250);
            uiGroupBox10.Font = new Font("宋体", 14F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox10.Location = new Point(827, 394);
            uiGroupBox10.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox10.MinimumSize = new Size(1, 1);
            uiGroupBox10.Name = "uiGroupBox10";
            uiGroupBox10.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox10.RectColor = Color.FromArgb(0, 190, 172);
            uiGroupBox10.Size = new Size(348, 356);
            uiGroupBox10.Style = Sunny.UI.UIStyle.Custom;
            uiGroupBox10.TabIndex = 4;
            uiGroupBox10.Text = "总结论判定";
            uiGroupBox10.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // uiCheckBox8
            // 
            uiCheckBox8.CheckBoxColor = Color.FromArgb(0, 190, 172);
            uiCheckBox8.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiCheckBox8.ForeColor = Color.FromArgb(48, 48, 48);
            uiCheckBox8.Location = new Point(32, 231);
            uiCheckBox8.MinimumSize = new Size(1, 1);
            uiCheckBox8.Name = "uiCheckBox8";
            uiCheckBox8.Size = new Size(160, 29);
            uiCheckBox8.Style = Sunny.UI.UIStyle.Custom;
            uiCheckBox8.TabIndex = 7;
            uiCheckBox8.Text = "同轴度检测";
            // 
            // uiCheckBox7
            // 
            uiCheckBox7.CheckBoxColor = Color.FromArgb(0, 190, 172);
            uiCheckBox7.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiCheckBox7.ForeColor = Color.FromArgb(48, 48, 48);
            uiCheckBox7.Location = new Point(32, 196);
            uiCheckBox7.MinimumSize = new Size(1, 1);
            uiCheckBox7.Name = "uiCheckBox7";
            uiCheckBox7.Size = new Size(160, 29);
            uiCheckBox7.Style = Sunny.UI.UIStyle.Custom;
            uiCheckBox7.TabIndex = 6;
            uiCheckBox7.Text = "二维码检测";
            // 
            // uiCheckBox6
            // 
            uiCheckBox6.CheckBoxColor = Color.FromArgb(0, 190, 172);
            uiCheckBox6.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiCheckBox6.ForeColor = Color.FromArgb(48, 48, 48);
            uiCheckBox6.Location = new Point(32, 161);
            uiCheckBox6.MinimumSize = new Size(1, 1);
            uiCheckBox6.Name = "uiCheckBox6";
            uiCheckBox6.Size = new Size(160, 29);
            uiCheckBox6.Style = Sunny.UI.UIStyle.Custom;
            uiCheckBox6.TabIndex = 5;
            uiCheckBox6.Text = "蚀刻机刻印";
            // 
            // uiCheckBox5
            // 
            uiCheckBox5.CheckBoxColor = Color.FromArgb(0, 190, 172);
            uiCheckBox5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiCheckBox5.ForeColor = Color.FromArgb(48, 48, 48);
            uiCheckBox5.Location = new Point(32, 126);
            uiCheckBox5.MinimumSize = new Size(1, 1);
            uiCheckBox5.Name = "uiCheckBox5";
            uiCheckBox5.Size = new Size(160, 29);
            uiCheckBox5.Style = Sunny.UI.UIStyle.Custom;
            uiCheckBox5.TabIndex = 4;
            uiCheckBox5.Text = "耐压";
            // 
            // uiCheckBox3
            // 
            uiCheckBox3.CheckBoxColor = Color.FromArgb(0, 190, 172);
            uiCheckBox3.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiCheckBox3.ForeColor = Color.FromArgb(48, 48, 48);
            uiCheckBox3.Location = new Point(32, 56);
            uiCheckBox3.MinimumSize = new Size(1, 1);
            uiCheckBox3.Name = "uiCheckBox3";
            uiCheckBox3.Size = new Size(189, 29);
            uiCheckBox3.Style = Sunny.UI.UIStyle.Custom;
            uiCheckBox3.TabIndex = 3;
            uiCheckBox3.Text = "高度检测";
            // 
            // uiButton11
            // 
            uiButton11.FillColor = Color.FromArgb(0, 190, 172);
            uiButton11.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton11.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton11.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton11.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton11.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton11.LightColor = Color.FromArgb(238, 251, 250);
            uiButton11.Location = new Point(201, 274);
            uiButton11.MinimumSize = new Size(1, 1);
            uiButton11.Name = "uiButton11";
            uiButton11.RectColor = Color.FromArgb(0, 190, 172);
            uiButton11.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton11.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton11.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton11.Size = new Size(125, 44);
            uiButton11.Style = Sunny.UI.UIStyle.Custom;
            uiButton11.TabIndex = 2;
            uiButton11.Text = "保存";
            uiButton11.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton11.Click += uiButton11_Click;
            // 
            // uiCheckBox4
            // 
            uiCheckBox4.CheckBoxColor = Color.FromArgb(0, 190, 172);
            uiCheckBox4.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiCheckBox4.ForeColor = Color.FromArgb(48, 48, 48);
            uiCheckBox4.Location = new Point(32, 91);
            uiCheckBox4.MinimumSize = new Size(1, 1);
            uiCheckBox4.Name = "uiCheckBox4";
            uiCheckBox4.Size = new Size(160, 29);
            uiCheckBox4.Style = Sunny.UI.UIStyle.Custom;
            uiCheckBox4.TabIndex = 0;
            uiCheckBox4.Text = "外观检测";
            // 
            // uiGroupBox9
            // 
            uiGroupBox9.Controls.Add(uiLabel30);
            uiGroupBox9.Controls.Add(uiComboBox4);
            uiGroupBox9.Controls.Add(uiButton10);
            uiGroupBox9.FillColor = Color.FromArgb(238, 251, 250);
            uiGroupBox9.FillColor2 = Color.FromArgb(238, 251, 250);
            uiGroupBox9.Font = new Font("宋体", 14F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox9.Location = new Point(435, 394);
            uiGroupBox9.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox9.MinimumSize = new Size(1, 1);
            uiGroupBox9.Name = "uiGroupBox9";
            uiGroupBox9.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox9.RectColor = Color.FromArgb(0, 190, 172);
            uiGroupBox9.Size = new Size(348, 356);
            uiGroupBox9.Style = Sunny.UI.UIStyle.Custom;
            uiGroupBox9.TabIndex = 15;
            uiGroupBox9.Text = "二维码检测";
            uiGroupBox9.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // uiLabel30
            // 
            uiLabel30.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel30.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel30.Location = new Point(21, 65);
            uiLabel30.Name = "uiLabel30";
            uiLabel30.Size = new Size(96, 29);
            uiLabel30.TabIndex = 14;
            uiLabel30.Text = "蚀刻等级:";
            // 
            // uiComboBox4
            // 
            uiComboBox4.DataSource = null;
            uiComboBox4.FillColor = Color.White;
            uiComboBox4.FillColor2 = Color.FromArgb(238, 251, 250);
            uiComboBox4.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiComboBox4.ItemHoverColor = Color.FromArgb(204, 242, 238);
            uiComboBox4.ItemRectColor = Color.FromArgb(0, 190, 172);
            uiComboBox4.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F" });
            uiComboBox4.ItemSelectBackColor = Color.FromArgb(0, 190, 172);
            uiComboBox4.ItemSelectForeColor = Color.FromArgb(238, 251, 250);
            uiComboBox4.Location = new Point(124, 56);
            uiComboBox4.Margin = new Padding(4, 5, 4, 5);
            uiComboBox4.MinimumSize = new Size(63, 0);
            uiComboBox4.Name = "uiComboBox4";
            uiComboBox4.Padding = new Padding(0, 0, 30, 2);
            uiComboBox4.RectColor = Color.FromArgb(0, 190, 172);
            uiComboBox4.Size = new Size(202, 38);
            uiComboBox4.Style = Sunny.UI.UIStyle.Custom;
            uiComboBox4.SymbolSize = 24;
            uiComboBox4.TabIndex = 3;
            uiComboBox4.TextAlignment = ContentAlignment.MiddleLeft;
            uiComboBox4.Watermark = "";
            // 
            // uiButton10
            // 
            uiButton10.FillColor = Color.FromArgb(0, 190, 172);
            uiButton10.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton10.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton10.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton10.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton10.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton10.LightColor = Color.FromArgb(238, 251, 250);
            uiButton10.Location = new Point(201, 147);
            uiButton10.MinimumSize = new Size(1, 1);
            uiButton10.Name = "uiButton10";
            uiButton10.RectColor = Color.FromArgb(0, 190, 172);
            uiButton10.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton10.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton10.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton10.Size = new Size(125, 44);
            uiButton10.Style = Sunny.UI.UIStyle.Custom;
            uiButton10.TabIndex = 7;
            uiButton10.Text = "保存";
            uiButton10.Click += uiButton10_Click;
            // 
            // uiGroupBox8
            // 
            uiGroupBox8.Controls.Add(uiButton3);
            uiGroupBox8.Controls.Add(uiButton2);
            uiGroupBox8.Controls.Add(uiTextBox31);
            uiGroupBox8.Controls.Add(uiLabel34);
            uiGroupBox8.FillColor = Color.FromArgb(238, 251, 250);
            uiGroupBox8.FillColor2 = Color.FromArgb(238, 251, 250);
            uiGroupBox8.Font = new Font("宋体", 14F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox8.Location = new Point(1223, 28);
            uiGroupBox8.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox8.MinimumSize = new Size(1, 1);
            uiGroupBox8.Name = "uiGroupBox8";
            uiGroupBox8.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox8.RectColor = Color.FromArgb(0, 190, 172);
            uiGroupBox8.Size = new Size(348, 340);
            uiGroupBox8.Style = Sunny.UI.UIStyle.Custom;
            uiGroupBox8.TabIndex = 5;
            uiGroupBox8.Text = "文件设置";
            uiGroupBox8.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // uiButton3
            // 
            uiButton3.FillColor = Color.FromArgb(0, 190, 172);
            uiButton3.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton3.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton3.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton3.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton3.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton3.LightColor = Color.FromArgb(238, 251, 250);
            uiButton3.Location = new Point(193, 111);
            uiButton3.MinimumSize = new Size(1, 1);
            uiButton3.Name = "uiButton3";
            uiButton3.RectColor = Color.FromArgb(0, 190, 172);
            uiButton3.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton3.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton3.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton3.Size = new Size(125, 44);
            uiButton3.Style = Sunny.UI.UIStyle.Custom;
            uiButton3.TabIndex = 9;
            uiButton3.Text = "保存";
            uiButton3.Click += uiButton3_Click;
            // 
            // uiButton2
            // 
            uiButton2.FillColor = Color.FromArgb(0, 190, 172);
            uiButton2.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton2.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton2.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton2.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton2.LightColor = Color.FromArgb(238, 251, 250);
            uiButton2.Location = new Point(26, 111);
            uiButton2.MinimumSize = new Size(1, 1);
            uiButton2.Name = "uiButton2";
            uiButton2.RectColor = Color.FromArgb(0, 190, 172);
            uiButton2.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton2.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton2.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton2.Size = new Size(125, 44);
            uiButton2.Style = Sunny.UI.UIStyle.Custom;
            uiButton2.TabIndex = 8;
            uiButton2.Text = "选择";
            uiButton2.Click += uiButton2_Click;
            // 
            // uiTextBox31
            // 
            uiTextBox31.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox31.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox31.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox31.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox31.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox31.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox31.ButtonStyleInherited = false;
            uiTextBox31.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox31.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox31.Location = new Point(130, 56);
            uiTextBox31.Margin = new Padding(4, 5, 4, 5);
            uiTextBox31.MinimumSize = new Size(1, 16);
            uiTextBox31.Name = "uiTextBox31";
            uiTextBox31.Padding = new Padding(5);
            uiTextBox31.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox31.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox31.ScrollBarStyleInherited = false;
            uiTextBox31.ShowText = false;
            uiTextBox31.Size = new Size(188, 36);
            uiTextBox31.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox31.TabIndex = 3;
            uiTextBox31.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox31.Watermark = "";
            // 
            // uiLabel34
            // 
            uiLabel34.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel34.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel34.Location = new Point(26, 63);
            uiLabel34.Name = "uiLabel34";
            uiLabel34.Size = new Size(97, 29);
            uiLabel34.TabIndex = 2;
            uiLabel34.Text = "保存目录:";
            // 
            // uiGroupBox7
            // 
            uiGroupBox7.Controls.Add(uiLabel24);
            uiGroupBox7.Controls.Add(uiComboBox2);
            uiGroupBox7.Controls.Add(uiButton1);
            uiGroupBox7.Controls.Add(uiButton9);
            uiGroupBox7.FillColor = Color.FromArgb(238, 251, 250);
            uiGroupBox7.FillColor2 = Color.FromArgb(238, 251, 250);
            uiGroupBox7.Font = new Font("宋体", 14F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox7.Location = new Point(827, 28);
            uiGroupBox7.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox7.MinimumSize = new Size(1, 1);
            uiGroupBox7.Name = "uiGroupBox7";
            uiGroupBox7.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox7.RectColor = Color.FromArgb(0, 190, 172);
            uiGroupBox7.Size = new Size(348, 340);
            uiGroupBox7.Style = Sunny.UI.UIStyle.Custom;
            uiGroupBox7.TabIndex = 4;
            uiGroupBox7.Text = "外观检查控制";
            uiGroupBox7.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // uiLabel24
            // 
            uiLabel24.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel24.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel24.Location = new Point(21, 65);
            uiLabel24.Name = "uiLabel24";
            uiLabel24.Size = new Size(96, 29);
            uiLabel24.TabIndex = 14;
            uiLabel24.Text = "下发编号:";
            // 
            // uiComboBox2
            // 
            uiComboBox2.DataSource = null;
            uiComboBox2.FillColor = Color.White;
            uiComboBox2.FillColor2 = Color.FromArgb(238, 251, 250);
            uiComboBox2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiComboBox2.ItemHoverColor = Color.FromArgb(204, 242, 238);
            uiComboBox2.ItemRectColor = Color.FromArgb(0, 190, 172);
            uiComboBox2.Items.AddRange(new object[] { "1", "2", "3", "4", "5", "6", "7", "8" });
            uiComboBox2.ItemSelectBackColor = Color.FromArgb(0, 190, 172);
            uiComboBox2.ItemSelectForeColor = Color.FromArgb(238, 251, 250);
            uiComboBox2.Location = new Point(124, 56);
            uiComboBox2.Margin = new Padding(4, 5, 4, 5);
            uiComboBox2.MinimumSize = new Size(63, 0);
            uiComboBox2.Name = "uiComboBox2";
            uiComboBox2.Padding = new Padding(0, 0, 30, 2);
            uiComboBox2.RectColor = Color.FromArgb(0, 190, 172);
            uiComboBox2.Size = new Size(202, 38);
            uiComboBox2.Style = Sunny.UI.UIStyle.Custom;
            uiComboBox2.SymbolSize = 24;
            uiComboBox2.TabIndex = 3;
            uiComboBox2.TextAlignment = ContentAlignment.MiddleLeft;
            uiComboBox2.Watermark = "";
            // 
            // uiButton1
            // 
            uiButton1.FillColor = Color.FromArgb(0, 190, 172);
            uiButton1.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton1.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton1.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton1.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton1.LightColor = Color.FromArgb(238, 251, 250);
            uiButton1.Location = new Point(201, 111);
            uiButton1.MinimumSize = new Size(1, 1);
            uiButton1.Name = "uiButton1";
            uiButton1.RectColor = Color.FromArgb(0, 190, 172);
            uiButton1.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton1.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton1.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton1.Size = new Size(125, 44);
            uiButton1.Style = Sunny.UI.UIStyle.Custom;
            uiButton1.TabIndex = 7;
            uiButton1.Text = "手动下发";
            uiButton1.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton1.Click += uiButton1_Click;
            // 
            // uiButton9
            // 
            uiButton9.FillColor = Color.FromArgb(0, 190, 172);
            uiButton9.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton9.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton9.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton9.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton9.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton9.LightColor = Color.FromArgb(238, 251, 250);
            uiButton9.Location = new Point(21, 111);
            uiButton9.MinimumSize = new Size(1, 1);
            uiButton9.Name = "uiButton9";
            uiButton9.RectColor = Color.FromArgb(0, 190, 172);
            uiButton9.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton9.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton9.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton9.Size = new Size(125, 44);
            uiButton9.Style = Sunny.UI.UIStyle.Custom;
            uiButton9.TabIndex = 6;
            uiButton9.Text = "自动下发";
            uiButton9.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton9.Click += uiButton9_Click;
            // 
            // uiGroupBox6
            // 
            uiGroupBox6.Controls.Add(uiComboBox5);
            uiGroupBox6.Controls.Add(uiLabel37);
            uiGroupBox6.Controls.Add(uiLabel36);
            uiGroupBox6.Controls.Add(uiLabel35);
            uiGroupBox6.Controls.Add(uiTextBox32);
            uiGroupBox6.Controls.Add(uiLabel33);
            uiGroupBox6.Controls.Add(uiTextBox30);
            uiGroupBox6.Controls.Add(uiTextBox29);
            uiGroupBox6.Controls.Add(uiLabel32);
            uiGroupBox6.Controls.Add(uiLabel31);
            uiGroupBox6.Controls.Add(uiTextBox25);
            uiGroupBox6.Controls.Add(uiComboBox3);
            uiGroupBox6.Controls.Add(uiTextBox5);
            uiGroupBox6.Controls.Add(uiLabel29);
            uiGroupBox6.Controls.Add(uiButton7);
            uiGroupBox6.Controls.Add(uiComboBox1);
            uiGroupBox6.Controls.Add(uiLabel28);
            uiGroupBox6.Controls.Add(uiTextBox28);
            uiGroupBox6.Controls.Add(uiLabel27);
            uiGroupBox6.Controls.Add(uiTextBox27);
            uiGroupBox6.Controls.Add(uiLabel26);
            uiGroupBox6.Controls.Add(uiTextBox26);
            uiGroupBox6.Controls.Add(uiLabel25);
            uiGroupBox6.Controls.Add(uiTextBox24);
            uiGroupBox6.Controls.Add(uiLabel23);
            uiGroupBox6.Controls.Add(uiTextBox23);
            uiGroupBox6.Controls.Add(uiLabel22);
            uiGroupBox6.FillColor = Color.FromArgb(238, 251, 250);
            uiGroupBox6.FillColor2 = Color.FromArgb(238, 251, 250);
            uiGroupBox6.Font = new Font("宋体", 14F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox6.Location = new Point(44, 28);
            uiGroupBox6.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox6.MinimumSize = new Size(1, 1);
            uiGroupBox6.Name = "uiGroupBox6";
            uiGroupBox6.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox6.RectColor = Color.FromArgb(0, 190, 172);
            uiGroupBox6.Size = new Size(348, 722);
            uiGroupBox6.Style = Sunny.UI.UIStyle.Custom;
            uiGroupBox6.TabIndex = 3;
            uiGroupBox6.Text = "耐压参数设置";
            uiGroupBox6.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // uiComboBox5
            // 
            uiComboBox5.DataSource = null;
            uiComboBox5.FillColor = Color.White;
            uiComboBox5.FillColor2 = Color.FromArgb(238, 251, 250);
            uiComboBox5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiComboBox5.ItemHoverColor = Color.FromArgb(204, 242, 238);
            uiComboBox5.ItemRectColor = Color.FromArgb(0, 190, 172);
            uiComboBox5.Items.AddRange(new object[] { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            uiComboBox5.ItemSelectBackColor = Color.FromArgb(0, 190, 172);
            uiComboBox5.ItemSelectForeColor = Color.FromArgb(238, 251, 250);
            uiComboBox5.Location = new Point(148, 613);
            uiComboBox5.Margin = new Padding(4, 5, 4, 5);
            uiComboBox5.MinimumSize = new Size(63, 0);
            uiComboBox5.Name = "uiComboBox5";
            uiComboBox5.Padding = new Padding(0, 0, 30, 2);
            uiComboBox5.RectColor = Color.FromArgb(0, 190, 172);
            uiComboBox5.Size = new Size(170, 38);
            uiComboBox5.Style = Sunny.UI.UIStyle.Custom;
            uiComboBox5.SymbolSize = 24;
            uiComboBox5.TabIndex = 9;
            uiComboBox5.TextAlignment = ContentAlignment.MiddleLeft;
            uiComboBox5.Watermark = "";
            // 
            // uiLabel37
            // 
            uiLabel37.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel37.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel37.Location = new Point(17, 622);
            uiLabel37.Name = "uiLabel37";
            uiLabel37.Size = new Size(115, 29);
            uiLabel37.TabIndex = 10;
            uiLabel37.Text = "电弧灵敏度:";
            // 
            // uiLabel36
            // 
            uiLabel36.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel36.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel36.Location = new Point(26, 573);
            uiLabel36.Name = "uiLabel36";
            uiLabel36.Size = new Size(106, 29);
            uiLabel36.TabIndex = 22;
            uiLabel36.Text = "测试时间S:";
            // 
            // uiLabel35
            // 
            uiLabel35.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel35.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel35.Location = new Point(26, 527);
            uiLabel35.Name = "uiLabel35";
            uiLabel35.Size = new Size(106, 29);
            uiLabel35.TabIndex = 20;
            uiLabel35.Text = "上升时间S:";
            // 
            // uiTextBox32
            // 
            uiTextBox32.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox32.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox32.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox32.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox32.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox32.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox32.ButtonStyleInherited = false;
            uiTextBox32.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox32.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox32.Location = new Point(148, 566);
            uiTextBox32.Margin = new Padding(4, 5, 4, 5);
            uiTextBox32.MinimumSize = new Size(1, 16);
            uiTextBox32.Name = "uiTextBox32";
            uiTextBox32.Padding = new Padding(5);
            uiTextBox32.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox32.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox32.ScrollBarStyleInherited = false;
            uiTextBox32.ShowText = false;
            uiTextBox32.Size = new Size(170, 36);
            uiTextBox32.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox32.TabIndex = 21;
            uiTextBox32.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox32.Watermark = "";
            // 
            // uiLabel33
            // 
            uiLabel33.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel33.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel33.Location = new Point(17, 481);
            uiLabel33.Name = "uiLabel33";
            uiLabel33.Size = new Size(115, 29);
            uiLabel33.TabIndex = 18;
            uiLabel33.Text = "直流下限mA:";
            // 
            // uiTextBox30
            // 
            uiTextBox30.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox30.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox30.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox30.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox30.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox30.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox30.ButtonStyleInherited = false;
            uiTextBox30.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox30.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox30.Location = new Point(148, 520);
            uiTextBox30.Margin = new Padding(4, 5, 4, 5);
            uiTextBox30.MinimumSize = new Size(1, 16);
            uiTextBox30.Name = "uiTextBox30";
            uiTextBox30.Padding = new Padding(5);
            uiTextBox30.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox30.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox30.ScrollBarStyleInherited = false;
            uiTextBox30.ShowText = false;
            uiTextBox30.Size = new Size(170, 36);
            uiTextBox30.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox30.TabIndex = 19;
            uiTextBox30.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox30.Watermark = "";
            // 
            // uiTextBox29
            // 
            uiTextBox29.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox29.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox29.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox29.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox29.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox29.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox29.ButtonStyleInherited = false;
            uiTextBox29.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox29.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox29.Location = new Point(148, 474);
            uiTextBox29.Margin = new Padding(4, 5, 4, 5);
            uiTextBox29.MinimumSize = new Size(1, 16);
            uiTextBox29.Name = "uiTextBox29";
            uiTextBox29.Padding = new Padding(5);
            uiTextBox29.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox29.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox29.ScrollBarStyleInherited = false;
            uiTextBox29.ShowText = false;
            uiTextBox29.Size = new Size(170, 36);
            uiTextBox29.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox29.TabIndex = 17;
            uiTextBox29.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox29.Watermark = "";
            // 
            // uiLabel32
            // 
            uiLabel32.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel32.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel32.Location = new Point(17, 435);
            uiLabel32.Name = "uiLabel32";
            uiLabel32.Size = new Size(115, 29);
            uiLabel32.TabIndex = 16;
            uiLabel32.Text = "直流上限mA:";
            // 
            // uiLabel31
            // 
            uiLabel31.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel31.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel31.Location = new Point(26, 389);
            uiLabel31.Name = "uiLabel31";
            uiLabel31.Size = new Size(106, 29);
            uiLabel31.TabIndex = 14;
            uiLabel31.Text = "直流电压V:";
            // 
            // uiTextBox25
            // 
            uiTextBox25.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox25.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox25.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox25.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox25.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox25.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox25.ButtonStyleInherited = false;
            uiTextBox25.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox25.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox25.Location = new Point(148, 428);
            uiTextBox25.Margin = new Padding(4, 5, 4, 5);
            uiTextBox25.MinimumSize = new Size(1, 16);
            uiTextBox25.Name = "uiTextBox25";
            uiTextBox25.Padding = new Padding(5);
            uiTextBox25.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox25.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox25.ScrollBarStyleInherited = false;
            uiTextBox25.ShowText = false;
            uiTextBox25.Size = new Size(170, 36);
            uiTextBox25.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox25.TabIndex = 15;
            uiTextBox25.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox25.Watermark = "";
            // 
            // uiComboBox3
            // 
            uiComboBox3.DataSource = null;
            uiComboBox3.FillColor = Color.White;
            uiComboBox3.FillColor2 = Color.FromArgb(238, 251, 250);
            uiComboBox3.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiComboBox3.ItemHoverColor = Color.FromArgb(204, 242, 238);
            uiComboBox3.ItemRectColor = Color.FromArgb(0, 190, 172);
            uiComboBox3.Items.AddRange(new object[] { "AUTO", "100G", "300M", "30M", "3M", "300K" });
            uiComboBox3.ItemSelectBackColor = Color.FromArgb(0, 190, 172);
            uiComboBox3.ItemSelectForeColor = Color.FromArgb(238, 251, 250);
            uiComboBox3.Location = new Point(148, 56);
            uiComboBox3.Margin = new Padding(4, 5, 4, 5);
            uiComboBox3.MinimumSize = new Size(63, 0);
            uiComboBox3.Name = "uiComboBox3";
            uiComboBox3.Padding = new Padding(0, 0, 30, 2);
            uiComboBox3.RectColor = Color.FromArgb(0, 190, 172);
            uiComboBox3.Size = new Size(170, 38);
            uiComboBox3.Style = Sunny.UI.UIStyle.Custom;
            uiComboBox3.SymbolSize = 24;
            uiComboBox3.TabIndex = 9;
            uiComboBox3.TextAlignment = ContentAlignment.MiddleLeft;
            uiComboBox3.Watermark = "";
            uiComboBox3.TextChanged += uiComboBox3_TextChanged;
            // 
            // uiTextBox5
            // 
            uiTextBox5.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox5.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox5.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox5.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox5.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox5.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox5.ButtonStyleInherited = false;
            uiTextBox5.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox5.Location = new Point(148, 382);
            uiTextBox5.Margin = new Padding(4, 5, 4, 5);
            uiTextBox5.MinimumSize = new Size(1, 16);
            uiTextBox5.Name = "uiTextBox5";
            uiTextBox5.Padding = new Padding(5);
            uiTextBox5.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox5.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox5.ScrollBarStyleInherited = false;
            uiTextBox5.ShowText = false;
            uiTextBox5.Size = new Size(170, 36);
            uiTextBox5.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox5.TabIndex = 13;
            uiTextBox5.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox5.Watermark = "";
            // 
            // uiLabel29
            // 
            uiLabel29.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel29.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel29.Location = new Point(36, 65);
            uiLabel29.Name = "uiLabel29";
            uiLabel29.Size = new Size(106, 29);
            uiLabel29.TabIndex = 10;
            uiLabel29.Text = "样品名称:";
            // 
            // uiButton7
            // 
            uiButton7.FillColor = Color.FromArgb(0, 190, 172);
            uiButton7.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton7.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton7.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton7.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton7.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton7.LightColor = Color.FromArgb(238, 251, 250);
            uiButton7.Location = new Point(218, 672);
            uiButton7.MinimumSize = new Size(1, 1);
            uiButton7.Name = "uiButton7";
            uiButton7.RectColor = Color.FromArgb(0, 190, 172);
            uiButton7.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton7.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton7.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton7.Size = new Size(100, 35);
            uiButton7.Style = Sunny.UI.UIStyle.Custom;
            uiButton7.TabIndex = 13;
            uiButton7.Text = "保存";
            uiButton7.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton7.Click += uiButton7_Click;
            // 
            // uiComboBox1
            // 
            uiComboBox1.DataSource = null;
            uiComboBox1.FillColor = Color.White;
            uiComboBox1.FillColor2 = Color.FromArgb(238, 251, 250);
            uiComboBox1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiComboBox1.ItemHoverColor = Color.FromArgb(204, 242, 238);
            uiComboBox1.ItemRectColor = Color.FromArgb(0, 190, 172);
            uiComboBox1.Items.AddRange(new object[] { "AUTO", "100G", "300M", "30M", "3M", "300K" });
            uiComboBox1.ItemSelectBackColor = Color.FromArgb(0, 190, 172);
            uiComboBox1.ItemSelectForeColor = Color.FromArgb(238, 251, 250);
            uiComboBox1.Location = new Point(148, 196);
            uiComboBox1.Margin = new Padding(4, 5, 4, 5);
            uiComboBox1.MinimumSize = new Size(63, 0);
            uiComboBox1.Name = "uiComboBox1";
            uiComboBox1.Padding = new Padding(0, 0, 30, 2);
            uiComboBox1.RectColor = Color.FromArgb(0, 190, 172);
            uiComboBox1.Size = new Size(170, 38);
            uiComboBox1.Style = Sunny.UI.UIStyle.Custom;
            uiComboBox1.SymbolSize = 24;
            uiComboBox1.TabIndex = 4;
            uiComboBox1.TextAlignment = ContentAlignment.MiddleLeft;
            uiComboBox1.Watermark = "";
            // 
            // uiLabel28
            // 
            uiLabel28.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel28.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel28.Location = new Point(26, 343);
            uiLabel28.Name = "uiLabel28";
            uiLabel28.Size = new Size(106, 29);
            uiLabel28.TabIndex = 12;
            uiLabel28.Text = "测试时间S:";
            // 
            // uiTextBox28
            // 
            uiTextBox28.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox28.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox28.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox28.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox28.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox28.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox28.ButtonStyleInherited = false;
            uiTextBox28.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox28.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox28.Location = new Point(148, 336);
            uiTextBox28.Margin = new Padding(4, 5, 4, 5);
            uiTextBox28.MinimumSize = new Size(1, 16);
            uiTextBox28.Name = "uiTextBox28";
            uiTextBox28.Padding = new Padding(5);
            uiTextBox28.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox28.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox28.ScrollBarStyleInherited = false;
            uiTextBox28.ShowText = false;
            uiTextBox28.Size = new Size(170, 36);
            uiTextBox28.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox28.TabIndex = 11;
            uiTextBox28.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox28.Watermark = "";
            // 
            // uiLabel27
            // 
            uiLabel27.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel27.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel27.Location = new Point(17, 297);
            uiLabel27.Name = "uiLabel27";
            uiLabel27.Size = new Size(124, 29);
            uiLabel27.TabIndex = 10;
            uiLabel27.Text = "电阻下限Ω:";
            // 
            // uiTextBox27
            // 
            uiTextBox27.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox27.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox27.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox27.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox27.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox27.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox27.ButtonStyleInherited = false;
            uiTextBox27.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox27.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox27.Location = new Point(148, 290);
            uiTextBox27.Margin = new Padding(4, 5, 4, 5);
            uiTextBox27.MinimumSize = new Size(1, 16);
            uiTextBox27.Name = "uiTextBox27";
            uiTextBox27.Padding = new Padding(5);
            uiTextBox27.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox27.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox27.ScrollBarStyleInherited = false;
            uiTextBox27.ShowText = false;
            uiTextBox27.Size = new Size(170, 36);
            uiTextBox27.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox27.TabIndex = 11;
            uiTextBox27.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox27.Watermark = "";
            // 
            // uiLabel26
            // 
            uiLabel26.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel26.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel26.Location = new Point(17, 251);
            uiLabel26.Name = "uiLabel26";
            uiLabel26.Size = new Size(124, 29);
            uiLabel26.TabIndex = 10;
            uiLabel26.Text = "电阻上限Ω:";
            // 
            // uiTextBox26
            // 
            uiTextBox26.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox26.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox26.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox26.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox26.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox26.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox26.ButtonStyleInherited = false;
            uiTextBox26.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox26.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox26.Location = new Point(148, 244);
            uiTextBox26.Margin = new Padding(4, 5, 4, 5);
            uiTextBox26.MinimumSize = new Size(1, 16);
            uiTextBox26.Name = "uiTextBox26";
            uiTextBox26.Padding = new Padding(5);
            uiTextBox26.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox26.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox26.ScrollBarStyleInherited = false;
            uiTextBox26.ShowText = false;
            uiTextBox26.Size = new Size(170, 36);
            uiTextBox26.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox26.TabIndex = 9;
            uiTextBox26.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox26.Watermark = "";
            // 
            // uiLabel25
            // 
            uiLabel25.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel25.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel25.Location = new Point(36, 205);
            uiLabel25.Name = "uiLabel25";
            uiLabel25.Size = new Size(96, 29);
            uiLabel25.TabIndex = 8;
            uiLabel25.Text = "电阻量程:";
            // 
            // uiTextBox24
            // 
            uiTextBox24.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox24.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox24.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox24.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox24.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox24.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox24.ButtonStyleInherited = false;
            uiTextBox24.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox24.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox24.Location = new Point(148, 150);
            uiTextBox24.Margin = new Padding(4, 5, 4, 5);
            uiTextBox24.MinimumSize = new Size(1, 16);
            uiTextBox24.Name = "uiTextBox24";
            uiTextBox24.Padding = new Padding(5);
            uiTextBox24.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox24.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox24.ScrollBarStyleInherited = false;
            uiTextBox24.ShowText = false;
            uiTextBox24.Size = new Size(170, 36);
            uiTextBox24.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox24.TabIndex = 5;
            uiTextBox24.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox24.Watermark = "";
            // 
            // uiLabel23
            // 
            uiLabel23.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel23.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel23.Location = new Point(26, 157);
            uiLabel23.Name = "uiLabel23";
            uiLabel23.Size = new Size(106, 29);
            uiLabel23.TabIndex = 4;
            uiLabel23.Text = "试验电压V:";
            // 
            // uiTextBox23
            // 
            uiTextBox23.ButtonFillColor = Color.FromArgb(0, 190, 172);
            uiTextBox23.ButtonFillHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox23.ButtonFillPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox23.ButtonRectColor = Color.FromArgb(0, 190, 172);
            uiTextBox23.ButtonRectHoverColor = Color.FromArgb(51, 203, 189);
            uiTextBox23.ButtonRectPressColor = Color.FromArgb(0, 152, 138);
            uiTextBox23.ButtonStyleInherited = false;
            uiTextBox23.FillColor2 = Color.FromArgb(238, 251, 250);
            uiTextBox23.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox23.Location = new Point(148, 104);
            uiTextBox23.Margin = new Padding(4, 5, 4, 5);
            uiTextBox23.MinimumSize = new Size(1, 16);
            uiTextBox23.Name = "uiTextBox23";
            uiTextBox23.Padding = new Padding(5);
            uiTextBox23.RectColor = Color.FromArgb(0, 190, 172);
            uiTextBox23.ScrollBarColor = Color.FromArgb(0, 190, 172);
            uiTextBox23.ScrollBarStyleInherited = false;
            uiTextBox23.ShowText = false;
            uiTextBox23.Size = new Size(170, 36);
            uiTextBox23.Style = Sunny.UI.UIStyle.Custom;
            uiTextBox23.TabIndex = 3;
            uiTextBox23.TextAlignment = ContentAlignment.MiddleLeft;
            uiTextBox23.Watermark = "";
            // 
            // uiLabel22
            // 
            uiLabel22.Font = new Font("宋体", 14F, FontStyle.Regular, GraphicsUnit.Point);
            uiLabel22.ForeColor = Color.FromArgb(48, 48, 48);
            uiLabel22.Location = new Point(73, 111);
            uiLabel22.Name = "uiLabel22";
            uiLabel22.Size = new Size(68, 29);
            uiLabel22.TabIndex = 2;
            uiLabel22.Text = "端口:";
            // 
            // uiGroupBox3
            // 
            uiGroupBox3.Controls.Add(uiCheckBox9);
            uiGroupBox3.Controls.Add(uiCheckBox2);
            uiGroupBox3.Controls.Add(uiButton8);
            uiGroupBox3.Controls.Add(uiCheckBox1);
            uiGroupBox3.FillColor = Color.FromArgb(238, 251, 250);
            uiGroupBox3.FillColor2 = Color.FromArgb(238, 251, 250);
            uiGroupBox3.Font = new Font("宋体", 14F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox3.Location = new Point(435, 28);
            uiGroupBox3.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox3.MinimumSize = new Size(1, 1);
            uiGroupBox3.Name = "uiGroupBox3";
            uiGroupBox3.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox3.RectColor = Color.FromArgb(0, 190, 172);
            uiGroupBox3.Size = new Size(348, 340);
            uiGroupBox3.Style = Sunny.UI.UIStyle.Custom;
            uiGroupBox3.TabIndex = 2;
            uiGroupBox3.Text = "专机结论控制";
            uiGroupBox3.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // uiCheckBox2
            // 
            uiCheckBox2.CheckBoxColor = Color.FromArgb(0, 190, 172);
            uiCheckBox2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiCheckBox2.ForeColor = Color.FromArgb(48, 48, 48);
            uiCheckBox2.Location = new Point(32, 91);
            uiCheckBox2.MinimumSize = new Size(1, 1);
            uiCheckBox2.Name = "uiCheckBox2";
            uiCheckBox2.Size = new Size(189, 29);
            uiCheckBox2.Style = Sunny.UI.UIStyle.Custom;
            uiCheckBox2.TabIndex = 3;
            uiCheckBox2.Text = "跳过二维码编码验证";
            uiCheckBox2.CheckedChanged += uiCheckBox2_CheckedChanged;
            // 
            // uiButton8
            // 
            uiButton8.FillColor = Color.FromArgb(0, 190, 172);
            uiButton8.FillColor2 = Color.FromArgb(0, 190, 172);
            uiButton8.FillHoverColor = Color.FromArgb(51, 203, 189);
            uiButton8.FillPressColor = Color.FromArgb(0, 152, 138);
            uiButton8.FillSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton8.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton8.LightColor = Color.FromArgb(238, 251, 250);
            uiButton8.Location = new Point(32, 190);
            uiButton8.MinimumSize = new Size(1, 1);
            uiButton8.Name = "uiButton8";
            uiButton8.RectColor = Color.FromArgb(0, 190, 172);
            uiButton8.RectHoverColor = Color.FromArgb(51, 203, 189);
            uiButton8.RectPressColor = Color.FromArgb(0, 152, 138);
            uiButton8.RectSelectedColor = Color.FromArgb(0, 152, 138);
            uiButton8.Size = new Size(125, 44);
            uiButton8.Style = Sunny.UI.UIStyle.Custom;
            uiButton8.TabIndex = 2;
            uiButton8.Text = "编号归零";
            uiButton8.TipsFont = new Font("宋体", 9F, FontStyle.Regular, GraphicsUnit.Point);
            uiButton8.Click += uiButton8_Click;
            // 
            // uiCheckBox1
            // 
            uiCheckBox1.CheckBoxColor = Color.FromArgb(0, 190, 172);
            uiCheckBox1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiCheckBox1.ForeColor = Color.FromArgb(48, 48, 48);
            uiCheckBox1.Location = new Point(32, 56);
            uiCheckBox1.MinimumSize = new Size(1, 1);
            uiCheckBox1.Name = "uiCheckBox1";
            uiCheckBox1.Size = new Size(160, 29);
            uiCheckBox1.Style = Sunny.UI.UIStyle.Custom;
            uiCheckBox1.TabIndex = 0;
            uiCheckBox1.Text = "外观检查默认合格";
            uiCheckBox1.CheckedChanged += uiCheckBox1_CheckedChanged;
            // 
            // uiCheckBox9
            // 
            uiCheckBox9.CheckBoxColor = Color.FromArgb(0, 190, 172);
            uiCheckBox9.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            uiCheckBox9.ForeColor = Color.FromArgb(48, 48, 48);
            uiCheckBox9.Location = new Point(32, 126);
            uiCheckBox9.MinimumSize = new Size(1, 1);
            uiCheckBox9.Name = "uiCheckBox9";
            uiCheckBox9.Size = new Size(189, 29);
            uiCheckBox9.Style = Sunny.UI.UIStyle.Custom;
            uiCheckBox9.TabIndex = 4;
            uiCheckBox9.Text = "重复编码验证";
            uiCheckBox9.CheckedChanged += uiCheckBox9_CheckedChanged;
            // 
            // MainControl
            // 
            AutoScaleMode = AutoScaleMode.None;
            BackColor = Color.FromArgb(238, 251, 250);
            ClientSize = new Size(1670, 879);
            ControlBoxFillHoverColor = Color.FromArgb(51, 203, 189);
            Controls.Add(uiTabControl1);
            Name = "MainControl";
            RectColor = Color.FromArgb(0, 190, 172);
            Style = Sunny.UI.UIStyle.Custom;
            uiGroupBox1.ResumeLayout(false);
            uiTabControl1.ResumeLayout(false);
            tabPage1.ResumeLayout(false);
            tabPage2.ResumeLayout(false);
            uiGroupBox2.ResumeLayout(false);
            uiGroupBox5.ResumeLayout(false);
            uiGroupBox4.ResumeLayout(false);
            tabPage4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)uiDataGridView1).EndInit();
            tabPage5.ResumeLayout(false);
            uiGroupBox11.ResumeLayout(false);
            uiGroupBox10.ResumeLayout(false);
            uiGroupBox9.ResumeLayout(false);
            uiGroupBox8.ResumeLayout(false);
            uiGroupBox7.ResumeLayout(false);
            uiGroupBox6.ResumeLayout(false);
            uiGroupBox3.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private Sunny.UI.UIGroupBox uiGroupBox1;
        private Sunny.UI.UITextBox uiTextBox4;
        private Sunny.UI.UILabel uiLabel4;
        private Sunny.UI.UITextBox uiTextBox3;
        private Sunny.UI.UILabel uiLabel3;
        private Sunny.UI.UITextBox uiTextBox2;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UITextBox uiTextBox1;
        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UITabControl uiTabControl1;
        private TabPage tabPage1;
        private TabPage tabPage2;
        private Sunny.UI.UIRichTextBox uiRichTextBox1;
        private TabPage tabPage4;
        private Sunny.UI.UIDataGridView uiDataGridView1;
        private TabPage tabPage5;
        private Sunny.UI.UIGroupBox uiGroupBox3;
        private Sunny.UI.UICheckBox uiCheckBox1;
        private Sunny.UI.UITextBox uiTextBox11;
        private Sunny.UI.UILabel uiLabel10;
        private Sunny.UI.UITextBox uiTextBox10;
        private Sunny.UI.UILabel uiLabel9;
        private Sunny.UI.UITextBox uiTextBox9;
        private Sunny.UI.UILabel uiLabel5;
        private Sunny.UI.UIGroupBox uiGroupBox4;
        private Sunny.UI.UITextBox uiTextBox12;
        private Sunny.UI.UILabel uiLabel11;
        private Sunny.UI.UITextBox uiTextBox13;
        private Sunny.UI.UILabel uiLabel12;
        private Sunny.UI.UITextBox uiTextBox14;
        private Sunny.UI.UILabel uiLabel13;
        private Sunny.UI.UITextBox uiTextBox15;
        private Sunny.UI.UILabel uiLabel14;
        private Sunny.UI.UITextBox uiTextBox16;
        private Sunny.UI.UILabel uiLabel15;
        private Sunny.UI.UITextBox uiTextBox17;
        private Sunny.UI.UILabel uiLabel16;
        private Sunny.UI.UITextBox uiTextBox18;
        private Sunny.UI.UILabel uiLabel17;
        private Sunny.UI.UITextBox uiTextBox19;
        private Sunny.UI.UILabel uiLabel18;
        private Sunny.UI.UIButton uiButton4;
        private Sunny.UI.UIButton uiButton5;
        private Sunny.UI.UIGroupBox uiGroupBox5;
        private Sunny.UI.UIButton uiButton6;
        private Sunny.UI.UITextBox uiTextBox20;
        private Sunny.UI.UILabel uiLabel19;
        private Sunny.UI.UITextBox uiTextBox21;
        private Sunny.UI.UILabel uiLabel20;
        private Sunny.UI.UITextBox uiTextBox22;
        private Sunny.UI.UILabel uiLabel21;
        private Sunny.UI.UIGroupBox uiGroupBox2;
        private Sunny.UI.UITextBox uiTextBox6;
        private Sunny.UI.UILabel uiLabel6;
        private Sunny.UI.UITextBox uiTextBox7;
        private Sunny.UI.UILabel uiLabel7;
        private Sunny.UI.UITextBox uiTextBox8;
        private Sunny.UI.UILabel uiLabel8;
        private Sunny.UI.UIGroupBox uiGroupBox6;
        private Sunny.UI.UITextBox uiTextBox23;
        private Sunny.UI.UILabel uiLabel22;
        private Sunny.UI.UITextBox uiTextBox24;
        private Sunny.UI.UILabel uiLabel23;
        private Sunny.UI.UITextBox uiTextBox26;
        private Sunny.UI.UILabel uiLabel25;
        private Sunny.UI.UIComboBox uiComboBox1;
        private Sunny.UI.UILabel uiLabel28;
        private Sunny.UI.UITextBox uiTextBox28;
        private Sunny.UI.UILabel uiLabel27;
        private Sunny.UI.UITextBox uiTextBox27;
        private Sunny.UI.UILabel uiLabel26;
        private Sunny.UI.UIButton uiButton7;
        private Sunny.UI.UIGroupBox uiGroupBox7;
        private Sunny.UI.UIButton uiButton8;
        private Sunny.UI.UILabel uiLabel24;
        private Sunny.UI.UIComboBox uiComboBox2;
        private Sunny.UI.UIButton uiButton1;
        private Sunny.UI.UIButton uiButton9;
        private Sunny.UI.UIGroupBox uiGroupBox8;
        private Sunny.UI.UITextBox uiTextBox31;
        private Sunny.UI.UILabel uiLabel34;
        private Sunny.UI.UIButton uiButton3;
        private Sunny.UI.UIButton uiButton2;
        private Sunny.UI.UIComboBox uiComboBox3;
        private Sunny.UI.UILabel uiLabel29;
        private Sunny.UI.UIGroupBox uiGroupBox9;
        private Sunny.UI.UILabel uiLabel30;
        private Sunny.UI.UIComboBox uiComboBox4;
        private Sunny.UI.UIButton uiButton10;
        private Sunny.UI.UICheckBox uiCheckBox2;
        private Sunny.UI.UIGroupBox uiGroupBox10;
        private Sunny.UI.UICheckBox uiCheckBox7;
        private Sunny.UI.UICheckBox uiCheckBox6;
        private Sunny.UI.UICheckBox uiCheckBox5;
        private Sunny.UI.UICheckBox uiCheckBox3;
        private Sunny.UI.UIButton uiButton11;
        private Sunny.UI.UICheckBox uiCheckBox4;
        private Sunny.UI.UICheckBox uiCheckBox8;
        private Sunny.UI.UILabel uiLabel31;
        private Sunny.UI.UITextBox uiTextBox5;
        private Sunny.UI.UILabel uiLabel35;
        private Sunny.UI.UILabel uiLabel33;
        private Sunny.UI.UITextBox uiTextBox30;
        private Sunny.UI.UITextBox uiTextBox29;
        private Sunny.UI.UILabel uiLabel32;
        private Sunny.UI.UITextBox uiTextBox25;
        private Sunny.UI.UIGroupBox uiGroupBox11;
        private Sunny.UI.UIComboBox uiComboBox5;
        private Sunny.UI.UILabel uiLabel37;
        private Sunny.UI.UILabel uiLabel36;
        private Sunny.UI.UITextBox uiTextBox32;
        private Sunny.UI.UIRadioButton uiRadioButton1;
        private Sunny.UI.UIRadioButton uiRadioButton2;
        private Sunny.UI.UICheckBox uiCheckBox9;
    }
}
