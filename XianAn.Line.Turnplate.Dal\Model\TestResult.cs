﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XianAn.Line.Turnplate.Dal.Model
{
    public class TestResult
    {
        public string Id { get; set; }
        /// <summary>
        /// 样品编号
        /// </summary>
        public string? SampleNo { get; set; }
        /// <summary>
        /// 标识符
        /// </summary>
        public string? CheckBit { get; set; }
        /// <summary>
        /// 圆直径
        /// </summary>
        public string? CircleDiameter1 { get; set; }
        /// <summary>
        /// 圆直径结论
        /// </summary>
        public string? CircleDiameterRes1 { get; set; }
        /// <summary>
        /// 圆直径2
        /// </summary>
        public string? CircleDiameter2 { get; set; }
        /// <summary>
        /// 圆直径2结论
        /// </summary>
        public string? CircleDiameterRes2 { get; set; }
        /// <summary>
        /// 外观数据
        /// </summary>
        public string? AppearanceData { get; set; }
        /// <summary>
        /// 毛边
        /// </summary>
        public string? MBResult { get; set; }
        /// <summary>
        /// 外观结论
        /// </summary>
        public string? AppearanceResult { get; set; }
        /// <summary>
        /// 绝缘电阻数据
        /// </summary>
        public string? IrData { get; set; }
        /// <summary>
        /// 绝缘电阻结论
        /// </summary>
        public string? IrResult { get; set; }
        /// <summary>
        /// 直流电流数据
        /// </summary>
        public string? DcData { get; set; }
        /// <summary>
        /// 直流电流结论
        /// </summary>
        public string? DcResult { get; set; }
        /// <summary>
        /// 蚀刻机结论
        /// </summary>
        public string? EtchingMachineResult { get; set; }
        /// <summary>
        /// 二维码数据
        /// </summary>
        public string? TDCodeData { get; set; }
        /// <summary>
        /// 二维码等级
        /// </summary>
        public string? TDCodeLimit { get; set; }
        /// <summary>
        /// 二维码结论
        /// </summary>
        public string? TDCodeResult { get; set; }
        /// <summary>
        /// 高度数据1
        /// </summary>
        public string? HeightData1 { get; set; }
        /// <summary>
        /// 高度数据2
        /// </summary>
        public string? HeightData2{ get; set; }
        /// <summary>
        /// 高度数据3
        /// </summary>
        public string? HeightData3 { get; set; }
        /// <summary>
        /// 高度结论1
        /// </summary>
        public string? HeightResult1 { get; set; }
        /// <summary>
        /// 高度结论2
        /// </summary>
        public string? HeightResult2 { get; set; }
        /// <summary>
        /// 同轴度数据
        /// </summary>
        public string? ConcentricityData { get; set; }
        /// <summary>
        /// 同轴度结论
        /// </summary>
        public string? ConcentricityResult { get; set; }
        /// <summary>
        /// 下料标识
        /// 1:下料完成
        /// </summary>
        public string? LayOffMark { get; set; }
        /// <summary>
        /// 总结论
        /// </summary>
        public string? Conclusion { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

    }
}
