﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XianAn.Line.Turnplate.Dal.Model
{
    public class TestIndex
    {
        public string Id { get; set; }
        /// <summary>
        /// 样品名称
        /// </summary>
        public string? SampleName { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        public string? MaterielNo { get; set; }
        /// <summary>
        /// 编号
        /// </summary>
        public string? IndexNum { get; set; }
        /// <summary>
        /// 模板名称
        /// </summary>
        public string? ModelName { get; set; }
        /// <summary>
        /// 高度标准
        /// </summary>
        public double? HeightStandard { get; set; }
        /// <summary>
        /// 高度限值
        /// </summary>
        public double? HeightChangeLimit { get; set; }
        /// <summary>
        /// 样品样式
        /// </summary>
        public string? FileType { get; set; }
        /// <summary>
        /// 高度类型
        /// </summary>
        public string? HeightType { get; set; }
        /// <summary>
        /// 上拍命令
        /// </summary>
        public string? UpPhotoCommand { get; set; }
        /// <summary>
        /// 下拍命令
        /// </summary>
        public string? DownPhotoCommand { get; set; }
        /// <summary>
        /// 模板切换命令
        /// </summary>
        public string? ModelChangeCommand { get; set; }
        /// <summary>
        /// 高度补偿
        /// </summary>
        public string? HeightCompensate { get; set; }
        /// <summary>
        /// 是否需要标识符拍照
        /// </summary>
        public string? NeedMarkPhoto { get; set; }
        /// <summary>
        /// 高度标准2
        /// </summary>
        public double? HeightStandard2 { get; set; }
        /// <summary>
        /// 高度标准3
        /// </summary>
        public double? HeightStandard3 { get; set; }
        /// <summary>
        /// 高度限值2
        /// </summary>
        public double? HeightChangeLimit2 { get; set; }
        /// <summary>
        /// 高度限值3
        /// </summary>
        public double? HeightChangeLimit3 { get; set; }

        /// <summary>
        /// 圆直径标准
        /// </summary>
        public string? YZJLimit1 { get; set; }
        /// <summary>
        /// 圆直径标准2
        /// </summary>
        public string? YZJLimit2 { get; set; }
    }
}
