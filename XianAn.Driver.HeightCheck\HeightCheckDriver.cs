﻿using System.Text;
using Wisdom.Utils.Driver;

namespace XianAn.Driver.HeightCheck
{
    public class HeightCheckDriver : DriverBase
    {
        public HeightCheckDriver(string name) : base(name, new HeightCheckProtocal())
        {
            //GetUpProtocol<UcsUpProtocol>().OnReceive += Driver_OnReceive;
        }

        /// <summary>
        /// 读取高度
        /// </summary>
        /// <returns></returns>
        public bool ReadHeightData(out List<string> values)
        {
            values = new List<string>();
            try
            {
                string data = "M0";
                var state = Call(data);
                var backData = Encoding.ASCII.GetString(state.Data);
                Logger.Info($"数据转换{backData}");
                if (backData.Contains("ER")) 
                {
                    return false;
                }
                var results = backData.Split(',');
                var length = results.Count();
                if (length <= 1) 
                {
                    return false;
                }
                for (var i = 1; i < length; i++) 
                {
                    var doubleData  = double.Parse(results[i]) / 1000;
                    var stringData = doubleData.ToString();
                    //if (!stringData.Contains('+') && !stringData.Contains('-')) 
                    //{
                    //    stringData = '+' + stringData;
                    //}
                    values.Add(stringData);
                }
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 复位高度标准
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public bool ReHeighStandard(string code) 
        {
            try
            {
                string data = "SW," + code + ",001,+000000001";
                var state = Call(data);
                var backData = Encoding.ASCII.GetString(state.Data);
                Logger.Info($"数据转换{backData}");
                if (backData.Contains("ER"))
                {
                    return false;
                }
                var value = "SW," + code + ",001";
                if (backData == value)
                {
                    return true;
                }
                else 
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);
                return false;
            }
        }
    }
}
