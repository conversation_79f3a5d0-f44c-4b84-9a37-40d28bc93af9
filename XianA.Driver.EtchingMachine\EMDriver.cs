﻿using System.Text;
using Wisdom.Utils.Driver;

namespace XianA.Driver.EtchingMachine
{
    public class EMDriver : DriverBase
    {
        public EMDriver(string name) : base(name, new EMProtocal())
        {
            //GetUpProtocol<UcsUpProtocol>().OnReceive += Driver_OnReceive;
        }

        /// <summary>
        /// 初始化模板
        /// </summary>
        /// <param name="index">模板命</param>
        /// <returns></returns>
        public bool InitModel(string index)
        {
            try
            {
                string data = "ChangeModel;123;" + index;
                var state = Call(data);
                var result = Encoding.UTF8.GetString(state.Data);
                if (result.Contains("OK"))
                {
                    return true;
                }
                else 
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 发送刻印内容
        /// </summary>
        ///  <param name="name">对象名</param>
        /// <param name="value">数据</param>
        /// <returns></returns>
        public bool SendEngravingData(string name, string value)
        {
            try
            {
                if (name == null || value == null) 
                {
                    Logger.Error("刻印内容为空");
                    return false;
                }
                string data = "ChangeData;" + name + ";" + value;
                var state = Call(data);
                var result = Encoding.UTF8.GetString(state.Data);
                if (result.Contains("OK"))
                {
                    Logger.Info("刻印内容发送成功");
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 开始刻印内容
        /// </summary>
        /// <param name="datas">数据</param>
        /// <returns></returns>
        public bool StartEngraving()
        {
            try
            {
                string data = "Mark;123;123";
                var state = Call(data);
                var result = Encoding.UTF8.GetString(state.Data);
                if (result.Contains("OK"))
                {
                    Logger.Info("标记成功");
                    return true;
                }
                else
                {
                    Logger.Error("标记失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);
                return false;
            }
        }
    }
}
