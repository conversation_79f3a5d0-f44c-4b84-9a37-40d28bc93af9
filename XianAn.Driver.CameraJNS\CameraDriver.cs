﻿using System.Text;
using Wisdom.Utils.Driver;
using Wisdom.Utils.Driver.Protocols;
using static XianAn.Driver.CameraJNS.CameraProtocal;

namespace XianAn.Driver.CameraJNS
{
    public class CameraDriver : DriverBase
    {
        public bool FinishPZ = false;
        public string ResultPZ = null;

        public bool FinishQH = false;
        public string ResultQH = null;

        public bool FinishQHMB = false;
        public string ResultQHMB = null;

        public bool FinishYSZ = false;
        public string ResultYSZ = null;
        public CameraDriver(string name) : base(name, new CameraProtocal(), new UpProtocol())
        {
            GetUpProtocol<UpProtocol>().OnReceive += Driver_OnReceive;
        }

        private void Driver_OnReceive(object sender, DataEventArgs e)
        {
            var frame = GetUpProtocol<UpProtocol>().Encoding.GetString(e.Data).TrimEnd('\r');
            if (frame.Contains("ER"))
            {
                FinishPZ = false;
                ResultPZ = null;
                FinishQH = false;
                ResultQH = null;
                Logger.Error("上报异常");
                return;
            }
            else if (frame == "CWN")
            {
                FinishQH = true;
                ResultQH = frame;
                Logger.Info("切换方案完成");
            }
            else if (frame == "PL")
            {
                FinishQHMB = true;
                ResultQHMB = frame;
                Logger.Info("切换方模板完成");
            }
            else if (frame == "OF")
            {
                FinishYSZ = true;
                Logger.Info("预设值完成");
            }
            else if (frame == "TRG")
            {
                Logger.Info("拍照完成");
            }
            else
            {
                FinishPZ = true;
                ResultPZ = frame;
                Logger.Info("拍照结论返回");
            }
        }

        /// <summary>
        /// 设置方案
        /// </summary>
        /// <param name="command">切换命令</param>
        /// <returns></returns>
        public bool SetAction(string command)
        {
            try
            {
                FinishQH = false;
                ResultQH = null;
                string data = command;
                //if (action == "1")
                //{
                //    data = "PL,1,0";
                //}
                //else
                //{
                //    data = "PL,1,3";
                //}
                CallNoReply(data);
                //var result = CheckAction(state.Data);
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 设置模板
        /// </summary>
        /// <param name="command">切换命令</param>
        /// <returns></returns>
        public bool SetModel(string command)
        {
            try
            {
                FinishQHMB = false;
                ResultQHMB = null;
                string data = command;
                CallNoReply(data);
                //var result = CheckAction(state.Data);
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 拍照
        /// </summary>
        /// <param name="command">命令</param>
        /// <returns>拍照结论</returns>
        public bool TakePicture()
        {
            FinishPZ = false;
            ResultPZ = null;
            try
            {
                string data = "TRG";
                CallNoReply(data);
                //var result = CheckResult(state.Data,out string type);
                //value = type;
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 标识符拍照
        /// </summary>
        /// <param name="command">命令</param>
        /// <returns>拍照结论</returns>
        public bool TakePicture2()
        {
            FinishPZ = false;
            ResultPZ = null;
            try
            {
                string data = "T2";
                CallNoReply(data);
                //var result = CheckResult(state.Data,out string type);
                //value = type;
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 拍照预设值（标识符拍照使用）
        /// </summary>
        /// <param name="command">命令</param>
        /// <returns>拍照结论</returns>
        public bool PresetValue()
        {
            FinishYSZ = false;
            ResultYSZ = null;
            try
            {
                string data = "OF,01";
                CallNoReply(data);
                //var result = CheckResult(state.Data,out string type);
                //value = type;
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);
                return false;
            }
        }



        /// <summary>
        /// 检查动作解析
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private bool CheckAction(byte[] data)
        {
            try
            {
                var value = Encoding.UTF8.GetString(data);
                Logger.Info($"数据转换{value}");
                var results = value.Split(',');
                if (value == "PL")
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 检查结果解析
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public bool CheckResult(string value, out string type)
        {
            type = null;
            try
            {
                if (value == null) 
                {
                    return false;
                }
                if (value.Contains("ER"))
                {
                    return false;
                }
                var results = value.Split(',');
                if (results.Count() == 3)
                {
                    type = results[2];
                }
                else if (results.Count() == 2)
                {

                }
                else
                {
                    return false;
                }

                if (results[0] == "1" && results[1] == "0")
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, ex.Message);
                return false;
            }
        }

    }
}
