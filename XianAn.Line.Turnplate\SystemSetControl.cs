﻿using NLog;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Wisdom.Utils.Driver.Arg;
using XianA.Driver.EtchingMachine;
using XianAn.Driver.CameraJNS;
using XianAn.Driver.HeightCheck;
using XianAn.Driver.PLC;
using XianAn.Line.Turnplate.Parameters;
using XianAn.Line.Turnplate.UnityHelper;

namespace XianAn.Line.Turnplate
{
    public partial class SystemSetControl : UIPage
    {
        /// <summary>
        /// 日志
        /// </summary>
        private Logger logger = LogManager.GetCurrentClassLogger();
        /// <summary>
        /// 配置文件
        /// </summary>
        private RunConSetting runConSetting = RunConSetting.Current;
        /// <summary>
        /// plc
        /// </summary>
        private PlcDriver plcDriver;

        public SystemSetControl()
        {
            InitializeComponent();
            //plcDriver = new PlcDriver(runConSetting.PlcIP, runConSetting.PlcPort);
            //InitDriver();
        }

        /// 初始化驱动
        /// </summary>
        private void InitDriver()
        {
            try
            {
                //plc
                plcDriver.Connect();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
            }
        }

        /// <summary>
        /// 样品编号归零
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton1_Click(object sender, EventArgs e)
        {
            try
            {
                var plcre = plcDriver.WriteShort(PlcPoint.bhql, 1);
                if (plcre)
                {
                    UIMessageBox.ShowAsk("调用成功");
                }
                else
                {
                    UIMessageBox.ShowError("调用失败");
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 界面切换
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiTabControl1_Selected(object sender, TabControlEventArgs e)
        {
            try
            {
                //赋值
                if (e.TabPage == tabPage1)
                {
                    //初始化目录
                    uiTextBox1.Text = runConSetting.UpPictureFileAdd;
                    uiTextBox2.Text = runConSetting.DownPictureFileAdd;
                    uiTextBox3.Text = runConSetting.SaveFileAdd;
                }
                else if (e.TabPage == tabPage3)
                {
                    //初始化地址

                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        /// <summary>
        /// 进入界面初始化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiTabControl1_Enter(object sender, EventArgs e)
        {
            try
            {
                //初始化目录
                uiTextBox1.Text = runConSetting.UpPictureFileAdd;
                uiTextBox2.Text = runConSetting.DownPictureFileAdd;
                uiTextBox3.Text = runConSetting.SaveFileAdd;
                //初始化地址

            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        #region 目录地址

        /// <summary>
        /// 上拍照文件地址选择
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton2_Click(object sender, EventArgs e)
        {
            try
            {
                FolderBrowserDialog dialog = new FolderBrowserDialog();
                dialog.Description = "选择路径"; ;//左上角提示
                string path = string.Empty;

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    path = dialog.SelectedPath;//获取选中文件路径
                    uiTextBox1.Text = path;
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }
        /// <summary>
        /// 上拍照文件地址保存
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton7_Click(object sender, EventArgs e)
        {
            try
            {
                runConSetting.UpPictureFileAdd = uiTextBox1.Text;
                runConSetting.Save();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }
        /// <summary>
        /// 下拍照文件地址选择
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton3_Click(object sender, EventArgs e)
        {
            try
            {
                FolderBrowserDialog dialog = new FolderBrowserDialog();
                dialog.Description = "选择路径"; ;//左上角提示
                string path = string.Empty;

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    path = dialog.SelectedPath;//获取选中文件路径
                    uiTextBox2.Text = path;
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }
        /// <summary>
        /// 下拍照文件地址保存
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton6_Click(object sender, EventArgs e)
        {
            try
            {
                runConSetting.DownPictureFileAdd = uiTextBox2.Text;
                runConSetting.Save();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }
        /// <summary>
        /// 结论文件地址选择
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton4_Click(object sender, EventArgs e)
        {
            try
            {
                FolderBrowserDialog dialog = new FolderBrowserDialog();
                dialog.Description = "选择路径"; ;//左上角提示
                string path = string.Empty;

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    path = dialog.SelectedPath;//获取选中文件路径
                    uiTextBox3.Text = path;
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }
        /// <summary>
        /// 结论文件地址保存
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton5_Click(object sender, EventArgs e)
        {
            try
            {
                runConSetting.SaveFileAdd = uiTextBox3.Text;
                runConSetting.Save();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        #endregion

        #region 专机试验控制

        private void uiCheckBox1_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                //var data = uiCheckBox1.Checked ? "true" : "false";
                //runConSetting.PassWGJC = data;
                //runConSetting.Save();
            }
            catch (Exception ex) 
            {
                logger.Error(ex);
                UIMessageBox.ShowError(ex.Message);
            }
        }

        #endregion
    }
}
