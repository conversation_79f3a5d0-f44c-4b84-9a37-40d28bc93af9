﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Wisdom.Utils.Driver.Checker;
using Wisdom.Utils.Driver;
using Wisdom.Utils.Driver.Protocols;
using Wisdom.Utils.Driver.Rules;

namespace XianAn.Driver.HeightCheck
{
    public class HeightCheckProtocal : DownProtocolBase
    {
        public HeightCheckProtocal() : base(CheckType.Sum, Encoding.ASCII, new FixedEndRule(new byte[] { 0x0D,0x0A}))
        {

        }

        protected override IEnumerable<byte> GenRequest(IRequest request)
        {
            var list = new List<byte>();
            list.AddRange(request.Data);
            list.Add(0x0D);
            list.Add(0x0A);
            return list;
        }

        protected override IResponse? CheckResponse(IRequest request, byte[] response)
        {
            byte[] newArray = response.Take(response.Length - 2).ToArray();
            var res = new Response();
            res.Data = newArray;
            //res.Data=response.Skip(2).Take()5
            //res.Command =new byte[] { response[1] };
            //if (response[0] != 0x00) 
            //{
            //    throw new Exception("通信错误：返回地址位错误");
            //}
            //res.Data = response.Take(2).ToArray();
            return res;
        }
    }
}
