﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XianAn.Line.Turnplate.UnityHelper.Parameters
{
    public class SaveFileData
    {
        /// <summary>
        /// 样品编号
        /// </summary>
        [Description("样品编号")]
        public string? SampleNo { get; set; }
        /// <summary>
        /// 识别字符
        /// </summary>
        [Description("识别字符")]
        public string? CheckBit { get; set; }
        /// <summary>
        /// 圆直径
        /// </summary>
        [Description("圆上直径")]
        public string? CircleDiameter1 { get; set; }
        /// <summary>
        /// 圆直径
        /// </summary>
        [Description("圆下直径")]
        public string? CircleDiameter2 { get; set; }
        /// <summary>
        /// 外观数据
        /// </summary>
        [Description("外观数据")]
        public string? AppearanceData { get; set; }
        /// <summary>
        /// 绝缘电阻数据
        /// </summary>
        [Description("绝缘电阻数据")]
        public string? IrData { get; set; }
        /// <summary>
        /// 绝缘电阻结论
        /// </summary>
        [Description("绝缘电阻结论")]
        public string? IrResult { get; set; }
        /// <summary>
        /// 二维码数据
        /// </summary>
        [Description("二维码数据")]
        public string? TDCodeData { get; set; }
        /// <summary>
        /// 二维码等级
        /// </summary>
        [Description("二维码等级")]
        public string? TDCodeLimit { get; set; }
        /// <summary>
        /// 高度数据1
        /// </summary>
        [Description("高度数据1")]
        public string? HeightData1 { get; set; }
        /// <summary>
        /// 高度数据2
        /// </summary>
        [Description("高度数据2")]
        public string? HeightData2 { get; set; }
        /// <summary>
        /// 高度数据3
        /// </summary>
        [Description("高度数据3")]
        public string? HeightData3 { get; set; }
        /// <summary>
        /// 同轴度数据
        /// </summary>
        [Description("同轴度数据")]
        public string? ConcentricityData { get; set; }
        /// <summary>
        /// 保存时间
        /// </summary>
        [Description("保存时间")]
        public string? CreateTime { get; set; }
    }
}
