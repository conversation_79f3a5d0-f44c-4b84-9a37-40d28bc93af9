﻿using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XianAn.Line.Turnplate
{
    public class RichTextAppendHelper
    {
        public delegate void LogAppendDelegate(Color color, string text);
        public UIRichTextBox richTextBoxRemote { get; set; }
        public RichTextAppendHelper(UIRichTextBox richTextBoxRemote)
        {
            this.richTextBoxRemote = richTextBoxRemote;
        }
        /// <summary> 
        /// 追加显示文本 
        /// </summary> 
        /// <param name="color">文本颜色</param> 
        /// <param name="text">显示文本</param> 
        public void LogAppend(Color color, string text)
        {
            if (richTextBoxRemote.Text != "" && richTextBoxRemote.Text != null)
                richTextBoxRemote.AppendText("\n");
            richTextBoxRemote.SelectionColor = color;
            richTextBoxRemote.AppendText(text);
            richTextBoxRemote.ScrollToCaret();
        }

        /// <summary> 
        /// 显示错误日志 
        /// </summary> 
        /// <param name="text"></param> 
        public void LogError(string text)
        {
            LogAppendDelegate la = new LogAppendDelegate(LogAppend);
            richTextBoxRemote.BeginInvoke(la, Color.Red, DateTime.Now.ToString("●" + "yyyy-MM-dd HH:mm:ss ") + text);
        }
        /// <summary> 
        /// 显示警告信息 
        /// </summary> 
        /// <param name="text"></param> 
        public void LogWarning(string text)
        {
            LogAppendDelegate la = new LogAppendDelegate(LogAppend);
            richTextBoxRemote.BeginInvoke(la, Color.Blue, DateTime.Now.ToString("●" + "yyyy-MM-dd HH:mm:ss ") + text);
        }
        /// <summary> 
        /// 显示信息 
        /// </summary> 
        /// <param name="text"></param> 
        public void LogMessage(string text)
        {
            LogAppendDelegate la = new LogAppendDelegate(LogAppend);
            richTextBoxRemote.BeginInvoke(la, Color.Black, DateTime.Now.ToString("●" + "yyyy-MM-dd HH:mm:ss ") + text);
        }
    }
}
