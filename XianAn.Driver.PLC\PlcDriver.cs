﻿using EasyModbus;
using NLog;
using System.Net.Sockets;

namespace XianAn.Driver.PLC
{
    public class PlcDriver
    {
        /// <summary>
        /// 设备IP
        /// </summary>
        private string Ip;
        /// <summary>
        /// 设备端口
        /// </summary>
        private int Port;
        /// <summary>
        /// 日志
        /// </summary>
        private Logger logger = LogManager.GetCurrentClassLogger();
        /// <summary>
        /// 连接锁
        /// </summary>
        public readonly object object1 = new object();

        public readonly object object2 = new object();

        ModbusClient tcpClient;

        public PlcDriver(string ip, int port)
        {
            Ip = ip;
            Port = port;
            tcpClient = new ModbusClient(Ip, Port);
        }

        public bool Connect()
        {
            try
            {
                lock (object1)
                {
                    if (tcpClient.Connected)
                    {
                        return true;
                    }
                    else
                    {
                        tcpClient.Connect();
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        public bool Disconnect()
        {
            try
            {
                tcpClient.Disconnect();
                return true;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }
        /// <summary>
        /// 获取bool
        /// </summary>
        /// <param name="add">起始地址</param>
        /// <param name="num">数量</param>
        /// <returns></returns>
        public bool[] ReadBool(int add, int num)
        {
            try
            {
                lock (object2)
                {
                    if (tcpClient.Connected)
                    {
                        var addNow = add;
                        bool[] coilStatus = tcpClient.ReadCoils(addNow, num);
                        return coilStatus;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        Connect();
                        return new bool[0];
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return new bool[0];
            }
        }

        /// <summary>
        /// 写入bool
        /// </summary>
        /// <param name="add">地址</param>
        /// <param name="data">数据</param>
        /// <returns></returns>
        public bool WriteBool(int add, bool data)
        {
            try
            {
                lock (object2)
                {
                    if (tcpClient.Connected)
                    {
                        var addNow = add;
                        tcpClient.WriteSingleCoil(addNow, data);
                        return true;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        Connect();
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 获取int
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public int ReadInt(int add)
        {
            try
            {
                lock (object2)
                {
                    if (tcpClient.Connected)
                    {
                        var addNow = add;
                        int[] holdingRegisters = tcpClient.ReadHoldingRegisters(addNow, 2);
                        // 将两个 16 位寄存器合并为一个 32 位整数
                        // 低字节和高字节合并成一个 32 位整数
                        int result = (holdingRegisters[1] << 16) | holdingRegisters[0];
                        return result;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        Connect();
                        return -1;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return -1;
            }
        }

        /// <summary>
        /// 获取short
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public short ReadShort(int add)
        {
            try
            {
                lock (object2)
                {
                    if (tcpClient.Connected)
                    {
                        var addNow = add;
                        int[] holdingRegisters = tcpClient.ReadHoldingRegisters(addNow, 1);
                        var value = holdingRegisters[0];
                        short result = Convert.ToInt16(value);
                        return result;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        Connect();
                        return -1;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return -1;
            }
        }

        /// <summary>
        /// 获取short
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public bool WriteShort(int add,ushort data)
        {
            try
            {
                lock (object2)
                {
                    if (tcpClient.Connected)
                    {
                        var addNow = add;
                        int[] registerValues = new int[1];
                        registerValues[0] = data;
                        // 写入多个寄存器
                        tcpClient.WriteMultipleRegisters(addNow, registerValues);
                        return true;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        Connect();
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 写入int
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public bool WriteInt(int add,int data)
        {
            try
            {
                lock (object2)
                {
                    if (tcpClient.Connected)
                    {
                        //// 将 32 位整数拆分成两个 16 位整数（高字节和低字节）
                        int[] registerValues = new int[2];
                        registerValues[0] = (ushort)(data & 0xFFFF);  // 低字节
                        registerValues[1] = (ushort)((data >> 16) & 0xFFFF);  // 高字节

                        var addNow = add;
                        // 写入多个寄存器
                        tcpClient.WriteMultipleRegisters(addNow, registerValues);
                        return true;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        Connect();
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 获取double
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public double ReadDouble(int add)
        {
            try
            {
                lock (object2)
                {
                    if (tcpClient.Connected)
                    {
                        var addNow = add;
                        int[] holdingRegisters = tcpClient.ReadHoldingRegisters(addNow, 4);
                        // 合并 4 个 16 位寄存器成一个 64 位整数
                        long high = (long)holdingRegisters[0] << 48 | (long)holdingRegisters[1] << 32 |
                                    (long)holdingRegisters[2] << 16 | holdingRegisters[3];

                        // 将 64 位整数转换为 double
                        double result = BitConverter.Int64BitsToDouble(high);
                        return result;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        Connect();
                        return -1;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return -1;
            }
        }
    }
}
