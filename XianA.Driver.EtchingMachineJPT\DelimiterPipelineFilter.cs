﻿using SuperSocket.ProtoBase;
using System;
using System.Buffers;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XianA.Driver.EtchingMachineJPT
{
    public class DelimiterPipelineFilter : TerminatorPipelineFilter<StringPackageInfo>
    {
        public DelimiterPipelineFilter() : base(Encoding.Unicode.GetBytes("stop")) { }

        protected override StringPackageInfo DecodePackage(ref ReadOnlySequence<byte> buffer)
        {
            var text = Encoding.Unicode.GetString(buffer.ToArray());
            var data = new StringPackageInfo()
            {
                Key = "CustomKey",
                Body = text,
                Parameters = Array.Empty<string>(),
            };
            return data;
        }
    }
}
