﻿using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using XianAn.Line.Turnplate.Dal.Model;

namespace XianAn.Line.Turnplate.Dal
{
    public class MyDbContext : DbContext
    {
        public DbSet<TestResult> TestResult { get; set; }
        public DbSet<TestIndex> TestIndex { get; set; }
        public DbSet<NYSetData> NYSetData { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder options)
            => options.UseSqlite("Data Source=database.db;", x => x.CommandTimeout(30));
    }
}
