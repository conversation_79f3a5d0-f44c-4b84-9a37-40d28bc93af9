﻿using Sunny.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Wisdom.Utils;

namespace XianAn.Line.Turnplate
{
    public partial class MainForm : UIHeaderAsideMainFooterFrame
    {
        private Repeater sjRepeater;
        public MainForm()
        {
            InitializeComponent();
            sjRepeater = new Repeater(TimeSpan.FromSeconds(1), ReadTime, 1000);
            //设置关联
            Aside.TabControl = MainTabControl;

            //增加页面到Main
            AddPage(new MainControl(), 1001);
            //AddPage(new SystemSetControl(), 1002);
            //AddPage(new FTitlePage3(), 1003);

            //设置Header节点索引
            Aside.CreateNode("主控", 1001);
            //Aside.CreateNode("系统设置", 1002);
            //Aside.CreateNode("Page3", 1003);

            //显示默认界面
            Aside.SelectFirst();

            sjRepeater.Start();
        }

        private void Aside_MenuItemClick(TreeNode node, NavMenuItem item, int pageIndex)
        {
            //sjRepeater.Start();
            
        }

        public void ReadTime() 
        {
            try 
            {
                var time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                Footer.Text = time;
            }
            catch(Exception ex) { }
        }
    }
}
