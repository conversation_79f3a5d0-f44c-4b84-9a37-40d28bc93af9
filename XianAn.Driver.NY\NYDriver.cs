﻿using EasyModbus;
using NLog;
using System.IO.Ports;
using System.Net.Http;
using System.Net.Sockets;
using System.Reflection;
using System.Timers;

namespace XianAn.Driver.NY
{
    public class NYDriver
    {
        /// <summary>
        /// 日志
        /// </summary>
        private Logger logger = LogManager.GetCurrentClassLogger();
        /// <summary>
        /// 连接锁
        /// </summary>
        public readonly object object1 = new object();

        public readonly object object2 = new object();

        ModbusClient rtuClient;
        //端口名称
        string targetPort = "COM3"; 

        public NYDriver(string serialPort, int baudRate = 9600, Parity parity = Parity.None)
        {
            targetPort = serialPort;
            rtuClient = new ModbusClient(serialPort);
            rtuClient.Baudrate = baudRate;
            rtuClient.Parity = parity;
        }

        public bool Connect()
        {
            try
            {
                lock (object1)
                {
                    if (rtuClient.Connected)
                    {
                        return true;
                    }
                    else
                    {
                        rtuClient.Connect();
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        public bool Disconnect()
        {
            try
            {
                rtuClient.Disconnect();
                return true;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 设置测试组编号
        /// </summary>
        /// <param name="group">1:M1,2:M2,3:M3</param>
        /// <returns></returns>
        public bool SetTestGroup(ushort group) 
        {
            try
            {
                var result = WriteShort(0x4000, group);
                return result;
            }
            catch (Exception ex) 
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 设置测试模式
        /// </summary>
        /// <param name="model">1:AC,2:DC,3:IR</param>
        /// <returns></returns>
        public bool SetTestModel(ushort model) 
        {
            try
            {
                var result = WriteShort(0x4001, model);
                return result;
            }
            catch (Exception ex) 
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 设置试验状态
        /// </summary>
        /// <param name="type">0：停止，1：开始</param>
        /// <returns></returns>
        public bool SetTest(ushort type) 
        {
            try
            {
                var result = WriteShort(0x4004, type);
                return result;
            }
            catch (Exception ex) 
            {
                logger.Error(ex);
                return false;
            }
        }

        #region 绝缘电阻

        /// <summary>
        /// 设置绝缘电阻电压值
        /// </summary>
        /// <param name="voltage">电压V</param>
        /// <returns></returns>
        public bool SetIrVoltage(ushort voltage) 
        {
            try
            {
                var result = WriteShort(0x4030, voltage);
                return result;
            }
            catch (Exception ex) 
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 设置绝缘电阻量程
        /// </summary>
        /// <param name="range">1:100G,2:1G,3:100M,4:10M,5:1M</param>
        /// <returns></returns>
        public bool SetIrRange(ushort range) 
        {
            try
            {
                var result = WriteShort(0x4031, range);
                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 设置电阻上限
        /// </summary>
        /// <param name="limit">200kΩ～99G</param>
        /// <returns></returns>
        public bool SetIrUpperLimit(float limit)
        {
            try
            {
                var result = WriteFloat(0x4033, limit);
                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 设置电阻下限
        /// </summary>
        /// <param name="limit">200kΩ～99G</param>
        /// <returns></returns>
        public bool SetIrLowerLimit(float limit)
        {
            try
            {
                var result = WriteFloat(0x4035, limit);
                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 等待时间
        /// </summary>
        /// <param name="time">读取数据（十进制）/ 10 ： 0.4S～999.9S</param>
        /// <returns></returns>
        public bool SetIrWaitingTime(double time)
        {
            try
            {
                var timenow = (ushort)(time * 10);
                var result = WriteShort(0x4037, timenow);
                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 设置测试时间
        /// </summary>
        /// <param name="time">读取数据（十进制）/ 10 ： 0.0S～999.9S</param>
        /// <returns></returns>
        public bool SetIrTestTime(double time)
        {
            try
            {
                var timenow = (ushort)(time * 10);
                var result = WriteShort(0x4038, timenow);
                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        #endregion

        /// <summary>
        /// 读取第一次测试状态
        /// </summary>
        /// <returns>0x0001：等待测试  0x0002：测试完成</returns>
        public string ReadState1()
        {
            try
            {
                var result = ReadShort2(0x3001);
                return result.ToString();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return null;
            }
        }

        /// <summary>
        /// 读取第一次测试数据
        /// </summary>
        /// <returns></returns>
        public string ReadData1()
        {
            try
            {
                var result = ReadFloat2(0x300A);
                return result.ToString();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return null;
            }
        }

        /// <summary>
        /// 读取第一次测试结果
        /// </summary>
        /// <returns></returns>
        public string ReadResult1()
        {
            try
            {
                var result = ReadShort2(0x300C);
                return result.ToString();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return null;
            }
        }

        #region 直流电流

        /// <summary>
        /// 设置直流电流电压值
        /// </summary>
        /// <param name="voltage">电压V</param>
        /// <returns></returns>
        public bool SetDcVoltage(ushort voltage)
        {
            try
            {
                var result = WriteShort(0x4020, voltage);
                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 设置直流上限
        /// </summary>
        /// <param name="limit">0.01mA～6.00mA</param>
        /// <returns></returns>
        public bool SetDcUpperLimit(double limit)
        {
            try
            {
                var limitnow = (ushort)(limit * 100);
                var result = WriteShort(0x4021, limitnow);
                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 设置直流下限
        /// </summary>
        /// <param name="limit">0.01mA～6.00mA</param>
        /// <returns></returns>
        public bool SetDcLowerLimit(double limit)
        {
            try
            {
                var limitnow = (ushort)(limit * 100);
                var result = WriteShort(0x4022, limitnow);
                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 设置直流电流上升时间
        /// </summary>
        /// <param name="time">读取数据（十进制）/ 10 ： 0.0S～999.9S</param>
        /// <returns></returns>
        public bool SetDcRiseTime(double time)
        {
            try
            {
                var timenow = (ushort)(time * 10);
                var result = WriteShort(0x4023, timenow);
                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 设置直流电流测试时间
        /// </summary>
        /// <param name="time">读取数据（十进制）/ 10 ： 0.0S～999.9S</param>
        /// <returns></returns>
        public bool SetDcTestTime(double time)
        {
            try
            {
                var timenow = (ushort)(time * 10);
                var result = WriteShort(0x4024, timenow);
                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 设置直流电流灵敏度
        /// </summary>
        /// <param name="time">0~9（0代表关闭此功能）</param>
        /// <returns></returns>
        public bool SetDcSensitivity(ushort state)
        {
            try
            {
                var result = WriteShort(0x4025, state);
                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        #endregion

        /// <summary>
        /// 判断端口是否存在
        /// </summary>
        /// <returns></returns>
        private bool CheckPortExists()
        {
            
            bool portExists = SerialPort.GetPortNames().Contains(targetPort);

            if (!portExists)
            {
                // 端口已消失的处理逻辑
                logger.Error($"端口{targetPort}消失");
                return false;
            }
            else 
            {
                return true;
            }
        }

        /// <summary>
        /// 获取bool
        /// </summary>
        /// <param name="add">起始地址</param>
        /// <param name="num">数量</param>
        /// <returns></returns>
        public bool[] ReadBool(int add, int num)
        {
            try
            {
                lock (object2)
                {
                    var havePort = CheckPortExists();
                    if (!havePort) 
                    {
                        logger.Error("耐压断开连接");
                        //Connect();
                        return new bool[0];
                    }
                    if (rtuClient.Connected)
                    {
                        var addNow = add;
                        bool[] coilStatus = rtuClient.ReadCoils(addNow, num);
                        return coilStatus;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        //Connect();
                        return new bool[0];
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return new bool[0];
            }
        }

        /// <summary>
        /// 写入bool
        /// </summary>
        /// <param name="add">地址</param>
        /// <param name="data">数据</param>
        /// <returns></returns>
        public bool WriteBool(int add, bool data)
        {
            try
            {
                lock (object2)
                {
                    var havePort = CheckPortExists();
                    if (!havePort)
                    {
                        logger.Error("耐压断开连接");
                        //Connect();
                        return false;
                    }
                    if (rtuClient.Connected)
                    {
                        var addNow = add;
                        rtuClient.WriteSingleCoil(addNow, data);
                        return true;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        //Connect();
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 获取int
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public int ReadInt(int add)
        {
            try
            {
                lock (object2)
                {
                    var havePort = CheckPortExists();
                    if (!havePort)
                    {
                        logger.Error("耐压断开连接");
                        //Connect();
                        return -1;
                    }
                    if (rtuClient.Connected)
                    {
                        var addNow = add;
                        int[] holdingRegisters = rtuClient.ReadHoldingRegisters(addNow, 2);
                        // 将两个 16 位寄存器合并为一个 32 位整数
                        // 低字节和高字节合并成一个 32 位整数
                        int result = (holdingRegisters[0] << 16) | holdingRegisters[1];
                        return result;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        //Connect();
                        return -1;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return -1;
            }
        }

        /// <summary>
        /// 获取int（输入寄存器）
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public int ReadInt2(int add)
        {
            try
            {
                lock (object2)
                {
                    var havePort = CheckPortExists();
                    if (!havePort)
                    {
                        logger.Error("耐压断开连接");
                        //Connect();
                        return -1;
                    }
                    if (rtuClient.Connected)
                    {
                        var addNow = add;
                        int[] holdingRegisters = rtuClient.ReadInputRegisters(addNow, 2);
                        // 将两个 16 位寄存器合并为一个 32 位整数
                        // 低字节和高字节合并成一个 32 位整数
                        int result = (holdingRegisters[0] << 16) | holdingRegisters[1];
                        return result;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        //Connect();
                        return -1;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return -1;
            }
        }

        /// <summary>
        /// 获取float（输入寄存器）
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public float ReadFloat(int add)
        {
            try
            {
                lock (object2)
                {
                    var havePort = CheckPortExists();
                    if (!havePort)
                    {
                        logger.Error("耐压断开连接");
                        //Connect();
                        return -1;
                    }
                    if (rtuClient.Connected)
                    {
                        var addNow = add;
                        int[] holdingRegisters = rtuClient.ReadHoldingRegisters(addNow, 2);
                        // 按照 ABCD 顺序拼接字节（低字节在前）
                        byte[] byteArray = new byte[4];
                        byteArray[2] = (byte)(holdingRegisters[0] & 0xFF); // 低字节
                        byteArray[3] = (byte)(holdingRegisters[0] >> 8);  // 高字节
                        byteArray[0] = (byte)(holdingRegisters[1] & 0xFF); // 低字节
                        byteArray[1] = (byte)(holdingRegisters[1] >> 8);  // 高字节

                        // 将字节数组转换为 float
                        float floatValue = BitConverter.ToSingle(byteArray, 0);
                        return floatValue;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        //Connect();
                        return -1;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return -1;
            }
        }

        /// <summary>
        /// 获取float（输入寄存器）
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public float ReadFloat2(int add)
        {
            try
            {
                lock (object2)
                {
                    var havePort = CheckPortExists();
                    if (!havePort)
                    {
                        logger.Error("耐压断开连接");
                        //Connect();
                        return -1;
                    }
                    if (rtuClient.Connected)
                    {
                        var addNow = add;
                        //int[] holdingRegisters = rtuClient.ReadInputRegisters(addNow, 2);
                        int[] holdingRegisters = new int[2] { 32640,0};
                        // 将两个 short 值转换为字节数组
                        byte[] byteArray = new byte[4];

                        // 将第一个 short 转换为字节并放入 byteArray
                        byteArray[2] = (byte)(holdingRegisters[0] & 0xFF);  // 低字节
                        byteArray[3] = (byte)((holdingRegisters[0] >> 8) & 0xFF);  // 高字节

                        // 将第二个 short 转换为字节并放入 byteArray
                        byteArray[0] = (byte)(holdingRegisters[1] & 0xFF);  // 低字节
                        byteArray[1] = (byte)((holdingRegisters[1] >> 8) & 0xFF);  // 高字节

                        // 将字节数组转换为 float
                        float result = BitConverter.ToSingle(byteArray, 0);
                        //// 将两个 16 位寄存器合并为一个 32 位整数
                        //// 低字节和高字节合并成一个 32 位整数
                        //float result = (holdingRegisters[0] << 16) | holdingRegisters[1];
                        return result;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        //Connect();
                        return -1;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return -1;
            }
        }

        /// <summary>
        /// 获取short
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public short ReadShort(int add)
        {
            try
            {
                lock (object2)
                {
                    var havePort = CheckPortExists();
                    if (!havePort)
                    {
                        logger.Error("耐压断开连接");
                        //Connect();
                        return -1;
                    }
                    if (rtuClient.Connected)
                    {
                        var addNow = add;
                        int[] holdingRegisters = rtuClient.ReadHoldingRegisters(addNow, 1);
                        var value = holdingRegisters[0];
                        short result = Convert.ToInt16(value);
                        return result;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        //Connect();
                        return -1;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return -1;
            }
        }

        /// <summary>
        /// 获取short(输入寄存器)
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public short ReadShort2(int add)
        {
            try
            {
                lock (object2)
                {
                    var havePort = CheckPortExists();
                    if (!havePort)
                    {
                        logger.Error("耐压断开连接");
                        //Connect();
                        return -1;
                    }
                    if (rtuClient.Connected)
                    {
                        var addNow = add;
                        int[] holdingRegisters = rtuClient.ReadInputRegisters(addNow, 1);
                        var value = holdingRegisters[0];
                        short result = Convert.ToInt16(value);
                        return result;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        //Connect();
                        return -1;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return -1;
            }
        }

        /// <summary>
        /// 获取short
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public bool WriteShort(int add, ushort data)
        {
            try
            {
                lock (object2)
                {
                    var havePort = CheckPortExists();
                    if (!havePort)
                    {
                        logger.Error("耐压断开连接");
                        //Connect();
                        return false;
                    }
                    if (rtuClient.Connected)
                    {
                        var addNow = add;
                        int[] registerValues = new int[1];
                        registerValues[0] = data;
                        // 写入多个寄存器
                        rtuClient.WriteMultipleRegisters(addNow, registerValues);
                        return true;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        //Connect();
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 写入int
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public bool WriteInt(int add, int data)
        {
            try
            {
                lock (object2)
                {
                    var havePort = CheckPortExists();
                    if (!havePort)
                    {
                        logger.Error("耐压断开连接");
                        //Connect();
                        return false;
                    }
                    if (rtuClient.Connected)
                    {
                        //// 将 32 位整数拆分成两个 16 位整数（高字节和低字节）
                        int[] registerValues = new int[2];
                        registerValues[0] = (ushort)(data>>16 & 0xFFFF);  // 高字节
                        registerValues[1] = (ushort)((data) & 0xFFFF);  // 低字节

                        var addNow = add;
                        // 写入多个寄存器
                        rtuClient.WriteMultipleRegisters(addNow, registerValues);
                        return true;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        //Connect();
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 写入float
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public bool WriteFloat(int add, float data)
        {
            try
            {
                lock (object2)
                {
                    var havePort = CheckPortExists();
                    if (!havePort)
                    {
                        logger.Error("耐压断开连接");
                        //Connect();
                        return false;
                    }
                    if (rtuClient.Connected)
                    {
                        // 将浮动值转换为字节数组
                        byte[] byteArray = BitConverter.GetBytes(data);
                        // 确保字节顺序是 ABCD（低字节在前，高字节在后）
                        // 拆分为两个 16 位寄存器
                        int[] registers = new int[2];
                        registers[0] = BitConverter.ToUInt16(byteArray, 2);  // 低字节在第一个寄存器
                        registers[1] = BitConverter.ToUInt16(byteArray, 0);  // 高字节在第二个寄存器

                        var addNow = add;
                        // 写入多个寄存器
                        rtuClient.WriteMultipleRegisters(addNow, registers);
                        return true;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        //Connect();
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 获取double
        /// </summary>
        /// <param name="add">地址</param>
        /// <returns></returns>
        public double ReadDouble(int add)
        {
            try
            {
                lock (object2)
                {
                    var havePort = CheckPortExists();
                    if (!havePort)
                    {
                        logger.Error("耐压断开连接");
                        //Connect();
                        return -1;
                    }
                    if (rtuClient.Connected)
                    {
                        var addNow = add;
                        int[] holdingRegisters = rtuClient.ReadHoldingRegisters(addNow, 4);
                        // 合并 4 个 16 位寄存器成一个 64 位整数
                        long high = (long)holdingRegisters[0] << 48 | (long)holdingRegisters[1] << 32 |
                                    (long)holdingRegisters[2] << 16 | holdingRegisters[3];

                        // 将 64 位整数转换为 double
                        double result = BitConverter.Int64BitsToDouble(high);
                        return result;
                    }
                    else
                    {
                        logger.Error("plc断开连接");
                        //Connect();
                        return -1;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                return -1;
            }
        }
    }
}
